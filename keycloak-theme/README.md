# Thème Keycloak Workeem

Ce dossier contient le thème personnalisé Keycloak pour Workeem.

## Structure

```
keycloak-theme/
└── workeem/
    └── login/
        ├── theme.properties          # Configuration du thème
        ├── template.ftl              # Template principal
        ├── login.ftl                 # Page de connexion
        ├── messages/
        │   └── messages_fr.properties # Messages en français
        └── resources/
            └── css/
                └── workeem.css       # Styles personnalisés Workeem
```

## Installation

### 1. Copie<PERSON> le thème dans Keycloak

Copiez le dossier `workeem` dans le répertoire des thèmes de votre installation Keycloak :

```bash
# Pour une installation standard
cp -r keycloak-theme/workeem /opt/keycloak/themes/

# Pour une installation Docker
docker cp keycloak-theme/workeem keycloak-container:/opt/keycloak/themes/
```

### 2. Configurer le realm

1. Connectez-vous à la console d'administration Keycloak
2. Sélectionnez votre realm
3. Allez dans **Realm Settings** > **Themes**
4. Dans **Login Theme**, sélectionnez `workeem`
5. <PERSON><PERSON>z sur **Save**

### 3. Redémarrer Keycloak (si nécessaire)

```bash
# Pour une installation standard
systemctl restart keycloak

# Pour Docker
docker restart keycloak-container
```

## Personnalisation

### Couleurs

Les couleurs principales sont définies dans `resources/css/workeem.css` :

```css
:root {
  --workeem-primary: #7c3aed;        /* Violet principal */
  --workeem-primary-dark: #6d28d9;   /* Violet foncé */
  --workeem-primary-light: #8b5cf6;  /* Violet clair */
  --workeem-secondary: #e8e5ff;      /* Fond secondaire */
  --workeem-accent: #d4c5ff;         /* Accent */
}
```

### Messages

Les messages sont personnalisés dans `messages/messages_fr.properties`. Vous pouvez :

- Modifier les textes existants
- Ajouter de nouveaux messages
- Créer des fichiers pour d'autres langues (ex: `messages_en.properties`)

### Templates

Les templates FreeMarker (`.ftl`) peuvent être personnalisés pour :

- Modifier la structure HTML
- Ajouter des éléments personnalisés
- Changer le comportement des formulaires

## Fonctionnalités

### Design

- **Arrière-plan dégradé** : Gradient violet assorti à l'identité Workeem
- **Card moderne** : Interface avec effet de verre et ombres
- **Responsive** : Adaptation automatique aux écrans mobiles
- **Animations** : Transitions fluides sur les interactions

### Branding

- **Logo Workeem** : Affiché en haut de la page de connexion
- **Couleurs cohérentes** : Palette de couleurs Workeem
- **Typographie** : Police système moderne
- **Messages personnalisés** : Textes adaptés au contexte Workeem

### Accessibilité

- **Contraste** : Respect des standards d'accessibilité
- **Navigation clavier** : Support complet du clavier
- **Messages d'erreur** : Affichage clair des erreurs
- **Labels** : Étiquettes appropriées pour les champs

## Développement

### Modification des styles

1. Éditez `resources/css/workeem.css`
2. Redémarrez Keycloak ou videz le cache
3. Testez les modifications

### Modification des templates

1. Éditez les fichiers `.ftl`
2. Redémarrez Keycloak
3. Testez les modifications

### Debug

Pour déboguer le thème :

1. Activez les logs de développement dans Keycloak
2. Vérifiez les erreurs dans les logs
3. Utilisez les outils de développement du navigateur

## Support

Pour toute question ou problème avec le thème :

1. Vérifiez que le thème est correctement installé
2. Consultez les logs Keycloak
3. Testez avec le thème par défaut pour isoler le problème

## Versions

- **Keycloak** : Compatible avec Keycloak 20+
- **Navigateurs** : Chrome, Firefox, Safari, Edge (versions récentes)
- **Responsive** : Support mobile et tablette
