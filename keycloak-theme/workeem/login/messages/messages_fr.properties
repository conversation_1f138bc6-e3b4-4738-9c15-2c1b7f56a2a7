# Messages français pour le thème Workeem
loginTitle=Connexion - {0}
loginTitleHtml=Connexion à <strong>{0}</strong>
loginAccountTitle=Connexion à votre compte
username=Nom d'utilisateur
usernameOrEmail=Nom d'utilisateur ou email
password=Mot de passe
doLogIn=Se connecter
doRegister=S'inscrire
doForgotPassword=Mot de passe oublié ?
noAccount=Nouveau sur Workeem ?
rememberMe=Se souvenir de moi
loginTimeout=Le délai de connexion a expiré. Veuillez recommencer.
loginTooltip=Connexion
logoutConfirmTitle=Déconnexion
logoutConfirmHeader=Voulez-vous vraiment vous déconnecter ?
logoutConfirmAction=Déconnexion
backToApplication=« Retour à l'application
missingUsernameMessage=Veuillez saisir un nom d'utilisateur.
missingPasswordMessage=Veuillez saisir un mot de passe.
invalidUserMessage=Nom d'utilisateur ou mot de passe incorrect.
invalidPasswordMinLengthMessage=Mot de passe incorrect, minimum {0} caractères.
invalidPasswordMaxLengthMessage=Mot de passe incorrect, maximum {0} caractères.
invalidPasswordMinLowerCaseCharsMessage=Mot de passe incorrect, doit contenir au moins {0} caractère(s) minuscule(s).
invalidPasswordMinUpperCaseCharsMessage=Mot de passe incorrect, doit contenir au moins {0} caractère(s) majuscule(s).
invalidPasswordMinDigitsMessage=Mot de passe incorrect, doit contenir au moins {0} chiffre(s).
invalidPasswordMinSpecialCharsMessage=Mot de passe incorrect, doit contenir au moins {0} caractère(s) spéciaux.
invalidPasswordNotUsernameMessage=Mot de passe incorrect, ne doit pas être identique au nom d'utilisateur.
invalidPasswordRegexPatternMessage=Mot de passe incorrect, ne respecte pas le format requis.
invalidPasswordHistoryMessage=Mot de passe incorrect, ne doit pas être identique aux {0} derniers mots de passe.
invalidPasswordBlacklistedMessage=Mot de passe incorrect, mot de passe dans la liste noire.
invalidPasswordGenericMessage=Mot de passe incorrect.
accountDisabledMessage=Compte désactivé, contactez votre administrateur.
accountTemporarilyDisabledMessage=Compte temporairement désactivé, contactez votre administrateur ou réessayez plus tard.
expiredCodeMessage=Délai de connexion expiré. Veuillez recommencer.
expiredActionMessage=Action expirée. Veuillez continuer avec la connexion maintenant.
expiredActionTokenNoSessionMessage=Action expirée.
expiredActionTokenSessionExistsMessage=Action expirée. Veuillez recommencer.
missingFirstNameMessage=Veuillez saisir votre prénom.
maxLengthMessage=Doit contenir au maximum {0} caractères.
minLengthMessage=Doit contenir au minimum {0} caractères.
missingLastNameMessage=Veuillez saisir votre nom.
missingEmailMessage=Veuillez saisir votre email.
missingPasswordMessage=Veuillez saisir votre mot de passe.
notMatchPasswordMessage=Les mots de passe ne correspondent pas.
invalidEmailMessage=Adresse email invalide.
readOnlyUserMessage=Vous ne pouvez pas modifier votre compte car il est en lecture seule.
readOnlyUsernameMessage=Vous ne pouvez pas modifier votre nom d'utilisateur car il est en lecture seule.
readOnlyPasswordMessage=Vous ne pouvez pas modifier votre mot de passe car votre compte est en lecture seule.
successLogoutMessage=Vous avez été déconnecté.
successAccountUpdatedMessage=Votre compte a été mis à jour.
accountUpdatedMessage=Votre compte a été mis à jour.
accountPasswordUpdatedMessage=Votre mot de passe a été mis à jour.
noAccessMessage=Pas d'accès
failedToProcessResponseMessage=Échec du traitement de la réponse
httpsRequiredMessage=HTTPS requis
realmNotEnabledMessage=Realm non activé
invalidRequestMessage=Requête invalide
failedLogoutMessage=Échec de la déconnexion
unknownLoginRequesterMessage=Demandeur de connexion inconnu
loginRequesterNotEnabledMessage=Demandeur de connexion non activé
bearerOnlyMessage=Les applications Bearer-only ne sont pas autorisées à initier une connexion par navigateur
standardFlowDisabledMessage=Le client n'est pas autorisé à initier une connexion par navigateur avec le type de réponse donné. Le flux standard est désactivé pour le client.
implicitFlowDisabledMessage=Le client n'est pas autorisé à initier une connexion par navigateur avec le type de réponse donné. Le flux implicite est désactivé pour le client.
invalidRedirectUriMessage=URI de redirection invalide
unsupportedNameIdFormatMessage=Format NameIDFormat non supporté
invalidRequesterMessage=Demandeur invalide
registrationNotAllowedMessage=Inscription non autorisée
resetPasswordMessage=Vous devez changer votre mot de passe.
permissionNotApprovedMessage=Permission non approuvée.
noRelayStateInResponseMessage=Pas de relay state dans la réponse du fournisseur d'identité.
identityProviderAlreadyLinkedMessage=L'identité fédérée retournée par le fournisseur d'identité est déjà liée à un autre utilisateur.
insufficientPermissionMessage=Permissions insuffisantes pour lier les identités.
couldNotProceedWithAuthenticationRequestMessage=Impossible de procéder à la demande d'authentification au fournisseur d'identité.
couldNotObtainTokenMessage=Impossible d'obtenir le token du fournisseur d'identité.
unexpectedErrorRetrievingTokenMessage=Erreur inattendue lors de la récupération du token du fournisseur d'identité.
unexpectedErrorHandlingResponseMessage=Erreur inattendue lors du traitement de la réponse du fournisseur d'identité.
identityProviderAuthenticationFailedMessage=Échec de l'authentification. Impossible de s'authentifier avec le fournisseur d'identité.
identityProviderDifferentUserMessage=L'utilisateur authentifié {0} ne correspond pas à l'utilisateur attendu {1}
couldNotSendAuthenticationRequestMessage=Impossible d'envoyer la demande d'authentification au fournisseur d'identité.
unexpectedErrorHandlingRequestMessage=Erreur inattendue lors du traitement de la demande d'authentification au fournisseur d'identité.
invalidAccessCodeMessage=Code d'accès invalide.
sessionNotActiveMessage=Session non active.
invalidCodeMessage=Une erreur s'est produite, veuillez vous reconnecter via votre application.
identityProviderUnexpectedErrorMessage=Erreur inattendue lors de l'authentification avec le fournisseur d'identité
identityProviderMissingStateMessage=Paramètre state manquant dans la réponse du fournisseur d'identité.
identityProviderInvalidResponseMessage=Réponse invalide du fournisseur d'identité.
identityProviderInvalidSignatureMessage=Signature invalide dans la réponse du fournisseur d'identité.
identityProviderNotFoundMessage=Aucun fournisseur d'identité trouvé.
identityProviderLinkSuccess=Vous avez vérifié avec succès votre email. Vous pouvez maintenant retourner à votre application.
staleCodeMessage=Cette page n'est plus valide, veuillez retourner à votre application et vous reconnecter
realmSupportsNoCredentialsMessage=Realm ne supporte aucun type d'identifiant.
identityProviderNotUniqueMessage=Realm supporte plusieurs fournisseurs d'identité. Impossible de déterminer le fournisseur d'identité à utiliser pour s'authentifier.
emailVerifyInstruction1=Un email avec des instructions pour vérifier votre adresse email a été envoyé à {0}.
emailVerifyInstruction2=Vous n'avez pas reçu de code de vérification dans votre email ?
emailVerifyInstruction3=pour renvoyer l'email.
emailLinkIdpTitle=Lier {0}
emailLinkIdp1=Un email avec des instructions pour lier le compte {0} {1} avec votre compte a été envoyé à {2}.
emailLinkIdp2=Vous n'avez pas reçu de code de vérification dans votre email ?
emailLinkIdp3=pour renvoyer l'email.
emailLinkIdp4=Si vous avez déjà vérifié l'email dans un autre navigateur
emailLinkIdp5=pour continuer.
backToLogin=« Retour à la connexion
emailInstruction=Saisissez votre nom d'utilisateur ou adresse email et nous vous enverrons des instructions pour créer un nouveau mot de passe.
copyCodeInstruction=Veuillez copier ce code et le coller dans votre application :
pageExpiredTitle=Page expirée
pageExpiredMsg1=Pour redémarrer le processus de connexion
pageExpiredMsg2=Pour continuer le processus de connexion
personalInfo=Informations personnelles
role_admin=Administrateur
role_realm-admin=Administrateur du realm
role_create-realm=Créer realm
role_view-realm=Voir realm
role_view-users=Voir utilisateurs
role_view-applications=Voir applications
role_view-clients=Voir clients
role_view-events=Voir événements
role_view-identity-providers=Voir fournisseurs d'identité
role_manage-realm=Gérer realm
role_manage-users=Gérer utilisateurs
role_manage-applications=Gérer applications
role_manage-identity-providers=Gérer fournisseurs d'identité
role_manage-clients=Gérer clients
role_manage-events=Gérer événements
role_view-profile=Voir profil
role_manage-account=Gérer compte
role_manage-account-links=Gérer liens de compte
role_read-token=Lire token
role_offline-access=Accès hors ligne
client_account=Compte
client_security-admin-console=Console d'administration de sécurité
client_admin-cli=Admin CLI
client_realm-management=Gestion du realm
client_broker=Broker
requiredFields=Champs requis
allFieldsRequired=Tous les champs sont requis
backToApplication=« Retour à l'application
backTo=Retour à {0}
date=Date
event=Événement
ip=IP
client=Client
clients=Clients
clientScopes=Portées client
clientScopesExplain=Les portées client vous permettent de définir un ensemble commun de rôles de protocole et de mappers de revendications qui sont partagés entre plusieurs clients
inResource=dans
resourceMissingName=Ressource sans nom
resourceMissingUris=Ressource sans URI
scopeMissingName=Portée sans nom
policyMissingName=Politique sans nom
policyMissingDescription=Politique sans description
policyMissingConfig=Politique sans configuration
permissionMissingName=Permission sans nom
permissionMissingDescription=Permission sans description
permissionMissingResource=Permission sans ressource
permissionMissingPolicy=Permission sans politique
permissionMissingType=Permission sans type
realm_role=Rôle de realm
client_role=Rôle de client
requiredAction.CONFIGURE_TOTP=Configurer OTP
requiredAction.terms_and_conditions=Conditions d'utilisation
requiredAction.UPDATE_PASSWORD=Mettre à jour le mot de passe
requiredAction.UPDATE_PROFILE=Mettre à jour le profil
requiredAction.VERIFY_EMAIL=Vérifier l'email
doX509Login=Vous serez connecté en tant que :
clientCertificate=Certificat client :
noCertificate=[Aucun certificat]
pageNotFound=Page non trouvée
internalServerError=Une erreur interne du serveur s'est produite
console-username=Nom d'utilisateur :
console-password=Mot de passe :
console-otp=Code à usage unique :
console-new-password=Nouveau mot de passe :
console-confirm-password=Confirmer le mot de passe :
console-update-password=Votre mot de passe a expiré. Vous devez le changer.
console-verify-email=Vous devez vérifier votre adresse email. Un email de vérification a été envoyé à {0}. Veuillez vérifier votre boîte de réception et cliquer sur le lien dans l'email.
console-accept-terms=Accepter les conditions d'utilisation ? [y/n]:
console-accept-terms-text=Conditions d'utilisation à accepter :
finalDeletionConfirmation=Si vous supprimez votre compte, il ne peut pas être récupéré. Êtes-vous sûr de vouloir continuer ?
irreversibleAction=Cette action est irréversible
deleteAccountConfirm=Confirmation de suppression de compte
deleteAccount=Supprimer le compte
deletingImplies=La suppression de votre compte implique :
errasingData=Effacement de toutes vos données
loggingOutImmediately=Vous déconnecter immédiatement
accountUnusable=Toute utilisation ultérieure de l'application ne sera plus possible avec ce compte
missingUsernameMessage=Veuillez saisir un nom d'utilisateur.
missingPasswordMessage=Veuillez saisir un mot de passe.
