/* Th<PERSON> Keycloak Workeem - Exactement comme tenant-selection */

/* Variables CSS pour les couleurs Workeem EXACTES */
:root {
  --workeem-primary: #6E56CF;
  --workeem-primary-hover: #8B73D6;
  --workeem-primary-active: #5A47B8;
  --workeem-primary-soft: #EDE9F8;
  --workeem-text: #333;
  --workeem-text-light: #666;
  --workeem-text-muted: #6b7280;
  --workeem-border: #e1e5e9;
  --workeem-background: linear-gradient(135deg, #e8e5ff 0%, #d4c5ff 50%, #c8b5ff 100%);
  --workeem-card-bg: rgba(255, 255, 255, 0.95);
  --workeem-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Reset et base - EXACTEMENT comme tenant-selection */
* {
  box-sizing: border-box;
}

/* Container principal - EXACTEMENT comme tenant-selection */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Container de la page - EXACTEMENT comme tenant-selection */
.login-pf-page, .login-pf {
  min-height: 100vh !important;
  width: 100vw !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  background: var(--workeem-background) !important;
  padding: 40px 20px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1000 !important;
}

/* Masquer le header Keycloak par défaut */
#kc-header {
  display: none !important;
}

/* Section logo - EXACTEMENT comme tenant-selection */
.logo-section {
  text-align: center !important;
  margin-bottom: 40px !important;
}

.logo-section img {
  height: 80px !important;
  width: auto !important;
}

/* Card de connexion - EXACTEMENT comme tenant-selection */
.card-pf, .login-pf-page .card-pf {
  background: var(--workeem-card-bg) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 20px !important;
  box-shadow: var(--workeem-shadow) !important;
  padding: 40px !important;
  width: 100% !important;
  max-width: 400px !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  margin: 0 !important;
}

/* Header de la card - EXACTEMENT comme tenant-selection */
.card-header, #kc-form-header {
  text-align: center !important;
  margin-bottom: 32px !important;
}

/* Titre principal - EXACTEMENT comme tenant-selection */
#kc-page-title, .card-pf-title, .card-header h1 {
  color: var(--workeem-text) !important;
  font-size: 18px !important;
  font-weight: 500 !important;
  margin: 0 0 8px 0 !important;
  line-height: 1.4 !important;
  text-align: center !important;
}

/* Sous-titre - EXACTEMENT comme tenant-selection */
.subtitle, .card-header p {
  color: var(--workeem-text-light) !important;
  font-size: 14px !important;
  margin: 0 !important;
  font-weight: 400 !important;
  text-align: center !important;
}

/* Formulaire - EXACTEMENT comme tenant-selection */
.login-form, #kc-form {
  margin-bottom: 32px !important;
}

.form-group, #kc-form-wrapper .form-group {
  margin-bottom: 24px !important;
}

/* Labels - EXACTEMENT comme tenant-selection */
.form-label, .control-label, label {
  display: block !important;
  color: #374151 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  margin-bottom: 8px !important;
}

/* Aide du formulaire - EXACTEMENT comme tenant-selection */
.form-help {
  color: var(--workeem-text-muted) !important;
  font-size: 12px !important;
  margin-top: 6px !important;
  line-height: 1.4 !important;
}

/* Wrapper d'input - EXACTEMENT comme tenant-selection */
.input-wrapper {
  position: relative !important;
}

/* Champs de saisie - EXACTEMENT comme tenant-selection */
.form-input, .form-control, input[type="text"], input[type="email"], input[type="password"] {
  width: 100% !important;
  height: 50px !important;
  padding: 0 16px !important;
  border: 1px solid var(--workeem-border) !important;
  border-radius: 12px !important;
  font-size: 16px !important;
  background: #f8f9fa !important;
  transition: all 0.2s ease !important;
  box-sizing: border-box !important;
  font-family: inherit !important;
}

/* Focus des champs - EXACTEMENT comme tenant-selection */
.form-input:focus, .form-control:focus, input[type="text"]:focus, input[type="email"]:focus, input[type="password"]:focus {
  outline: none !important;
  border-color: var(--workeem-primary) !important;
  background: white !important;
  box-shadow: 0 0 0 3px rgba(110, 86, 207, 0.1) !important;
}

/* Champs désactivés - EXACTEMENT comme tenant-selection */
.form-input:disabled, .form-control:disabled, input:disabled {
  background: #f5f5f5 !important;
  color: #999 !important;
  cursor: not-allowed !important;
}

/* Bouton de soumission - EXACTEMENT comme tenant-selection */
.submit-button, .btn-primary, input[type="submit"], button[type="submit"] {
  width: 100% !important;
  height: 50px !important;
  background: var(--workeem-primary) !important;
  color: white !important;
  border: none !important;
  border-radius: 12px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-family: inherit !important;
}

/* Hover du bouton - EXACTEMENT comme tenant-selection */
.submit-button:hover:not(:disabled), .btn-primary:hover:not(:disabled), input[type="submit"]:hover:not(:disabled), button[type="submit"]:hover:not(:disabled) {
  background: var(--workeem-primary-active) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(110, 86, 207, 0.3) !important;
}

/* Active du bouton - EXACTEMENT comme tenant-selection */
.submit-button:active:not(:disabled), .btn-primary:active:not(:disabled), input[type="submit"]:active:not(:disabled), button[type="submit"]:active:not(:disabled) {
  transform: translateY(0) !important;
}

/* Bouton désactivé - EXACTEMENT comme tenant-selection */
.submit-button:disabled, .btn-primary:disabled, input[type="submit"]:disabled, button[type="submit"]:disabled {
  background: #d1d5db !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Bouton en chargement - EXACTEMENT comme tenant-selection */
.submit-button.loading {
  background: var(--workeem-primary) !important;
}

/* Liens - EXACTEMENT comme tenant-selection */
a {
  color: var(--workeem-primary) !important;
  text-decoration: none !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

a:hover {
  color: var(--workeem-primary-active) !important;
  text-decoration: underline !important;
}

/* Messages d'erreur - Style moderne */
.alert-error, .pf-m-danger, .kc-feedback-text {
  background: #fee2e2 !important;
  border: 1px solid #fecaca !important;
  color: #dc2626 !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  margin-bottom: 20px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

/* Messages de succès */
.alert-success, .pf-m-success {
  background: #dcfce7 !important;
  border: 1px solid #bbf7d0 !important;
  color: #16a34a !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  margin-bottom: 20px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

/* Messages d'information */
.alert-info, .pf-m-info {
  background: #dbeafe !important;
  border: 1px solid #bfdbfe !important;
  color: #2563eb !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  margin-bottom: 20px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

/* Footer de la card - EXACTEMENT comme tenant-selection */
.card-footer {
  text-align: center !important;
  padding-top: 20px !important;
  border-top: 1px solid #f0f0f0 !important;
}

.card-footer p {
  color: #9ca3af !important;
  font-size: 12px !important;
  margin: 0 !important;
}

/* Checkboxes - Style moderne */
.checkbox input[type="checkbox"] {
  margin-right: 8px !important;
  transform: scale(1.1) !important;
}

.checkbox label {
  color: var(--workeem-text) !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
}

/* Options du formulaire - EXACTEMENT comme tenant-selection */
#kc-form-options {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 24px !important;
  font-size: 14px !important;
}

/* Instructions et texte d'aide */
.instruction {
  color: var(--workeem-text-light) !important;
  font-size: 14px !important;
  margin-bottom: 24px !important;
  text-align: center !important;
  line-height: 1.5 !important;
}

/* Masquer les éléments non nécessaires */
.login-pf-header {
  display: none !important;
}

/* Personnalisation du container */
.login-pf-page .container {
  max-width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 100vh !important;
}

/* Responsive Design - EXACTEMENT comme tenant-selection */
@media (max-width: 480px) {
  .login-pf-page, .login-pf {
    padding: 20px 16px !important;
  }

  .logo-section img {
    height: 60px !important;
  }

  .card-pf, .login-pf-page .card-pf {
    padding: 32px 24px !important;
  }

  #kc-page-title, .card-pf-title, .card-header h1 {
    font-size: 16px !important;
  }
}

/* Animations et transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-pf, .login-pf-page .card-pf {
  animation: fadeInUp 0.6s ease-out !important;
}

/* Amélioration de l'accessibilité */
.form-control:focus, input:focus, button:focus {
  outline: 2px solid var(--workeem-primary) !important;
  outline-offset: 2px !important;
}

/* Ajustements pour le formulaire */
#kc-form-wrapper {
  width: 100% !important;
}

/* Ajustement pour le lien "Mot de passe oublié" */
#kc-form-options a {
  font-size: 14px !important;
  color: var(--workeem-primary) !important;
  text-decoration: none !important;
}

#kc-form-options a:hover {
  text-decoration: underline !important;
  color: var(--workeem-primary-active) !important;
}
