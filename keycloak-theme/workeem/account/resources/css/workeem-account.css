/* Thème Keycloak Account Management Workeem - Identique à tenant-selection */

/* Variables CSS pour les couleurs Workeem EXACTES */
:root {
  --workeem-primary: #7c3aed;
  --workeem-primary-hover: #6d28d9;
  --workeem-primary-active: #5b21b6;
  --workeem-primary-soft: #ede9fe;
  --workeem-text: #333;
  --workeem-text-light: #666;
  --workeem-text-muted: #6b7280;
  --workeem-border: #e1e5e9;
  --workeem-background: linear-gradient(135deg, #e8e5ff 0%, #d4c5ff 50%, #c8b5ff 100%);
  --workeem-card-bg: rgba(255, 255, 255, 0.95);
  --workeem-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Reset et base */
* {
  box-sizing: border-box;
}

/* Container principal - EXACTEMENT comme tenant-selection */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', Robot<PERSON>, sans-serif !important;
  background: var(--workeem-background) !important;
}

/* Layout principal */
.layout-pf, .container-fluid, .row, .col-sm-12 {
  background: transparent !important;
  min-height: 100vh !important;
}

/* Header Keycloak */
.navbar-pf {
  background: var(--workeem-primary) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.navbar-pf .navbar-brand {
  color: white !important;
  font-weight: 600 !important;
  font-size: 18px !important;
}

.navbar-pf .navbar-nav > li > a {
  color: rgba(255, 255, 255, 0.9) !important;
}

.navbar-pf .navbar-nav > li > a:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.1) !important;
}

/* Sidebar navigation */
.nav-pf-vertical {
  background: var(--workeem-card-bg) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 20px !important;
  box-shadow: var(--workeem-shadow) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  margin: 20px !important;
}

.nav-pf-vertical .nav-pf-vertical-item > a {
  color: var(--workeem-text) !important;
  border-radius: 12px !important;
  margin: 4px 12px !important;
  padding: 12px 16px !important;
  transition: all 0.2s ease !important;
}

.nav-pf-vertical .nav-pf-vertical-item > a:hover {
  background: var(--workeem-primary-soft) !important;
  color: var(--workeem-primary) !important;
}

.nav-pf-vertical .nav-pf-vertical-item.active > a {
  background: var(--workeem-primary) !important;
  color: white !important;
}

/* Content area */
.content-area {
  background: var(--workeem-card-bg) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 20px !important;
  box-shadow: var(--workeem-shadow) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  margin: 20px !important;
  padding: 40px !important;
}

/* Forms */
.form-horizontal .form-group {
  margin-bottom: 24px !important;
}

.form-horizontal .control-label {
  color: var(--workeem-text) !important;
  font-weight: 600 !important;
  font-size: 14px !important;
}

.form-control {
  height: 50px !important;
  padding: 0 16px !important;
  border: 1px solid var(--workeem-border) !important;
  border-radius: 12px !important;
  font-size: 16px !important;
  background: #f8f9fa !important;
  transition: all 0.2s ease !important;
}

.form-control:focus {
  outline: none !important;
  border-color: var(--workeem-primary) !important;
  background: white !important;
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1) !important;
}

/* Buttons */
.btn-primary {
  background: var(--workeem-primary) !important;
  border-color: var(--workeem-primary) !important;
  color: white !important;
  border-radius: 12px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
}

.btn-primary:hover {
  background: var(--workeem-primary-hover) !important;
  border-color: var(--workeem-primary-hover) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3) !important;
}

.btn-primary:active {
  background: var(--workeem-primary-active) !important;
  border-color: var(--workeem-primary-active) !important;
  transform: translateY(0) !important;
}

.btn-default {
  background: white !important;
  border-color: var(--workeem-border) !important;
  color: var(--workeem-text) !important;
  border-radius: 12px !important;
  padding: 12px 24px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.btn-default:hover {
  background: var(--workeem-primary-soft) !important;
  border-color: var(--workeem-primary) !important;
  color: var(--workeem-primary) !important;
}

/* Cards et panels */
.panel {
  background: var(--workeem-card-bg) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 20px !important;
  box-shadow: var(--workeem-shadow) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  margin-bottom: 20px !important;
}

.panel-heading {
  background: transparent !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
  border-radius: 20px 20px 0 0 !important;
  padding: 20px 30px !important;
}

.panel-title {
  color: var(--workeem-text) !important;
  font-weight: 600 !important;
  font-size: 18px !important;
}

.panel-body {
  padding: 30px !important;
}

/* Tables */
.table {
  background: white !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

.table th {
  background: var(--workeem-primary-soft) !important;
  color: var(--workeem-text) !important;
  font-weight: 600 !important;
  border: none !important;
  padding: 16px !important;
}

.table td {
  border: none !important;
  padding: 16px !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

/* Alerts */
.alert-success {
  background: #d1fae5 !important;
  border: 1px solid #a7f3d0 !important;
  color: #065f46 !important;
  border-radius: 12px !important;
}

.alert-danger {
  background: #fee2e2 !important;
  border: 1px solid #fecaca !important;
  color: #991b1b !important;
  border-radius: 12px !important;
}

.alert-info {
  background: #dbeafe !important;
  border: 1px solid #bfdbfe !important;
  color: #1e40af !important;
  border-radius: 12px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-pf-vertical, .content-area {
    margin: 10px !important;
    padding: 20px !important;
  }
  
  .panel-heading, .panel-body {
    padding: 20px !important;
  }
}
