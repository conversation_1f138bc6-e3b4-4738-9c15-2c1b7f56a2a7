#!/bin/bash

# Script de déploiement du thème Workeem pour Keycloak
# Usage: ./deploy-theme.sh [keycloak-path]

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier si un paramètre est fourni
KEYCLOAK_PATH=${1:-"/opt/keycloak"}

log_info "Déploiement du thème Workeem pour Keycloak"
log_info "Chemin Keycloak: $KEYCLOAK_PATH"

# Vérifier si le répertoire Keycloak existe
if [ ! -d "$KEYCLOAK_PATH" ]; then
    log_error "Le répertoire Keycloak '$KEYCLOAK_PATH' n'existe pas."
    log_info "Usage: $0 [chemin-vers-keycloak]"
    log_info "Exemple: $0 /opt/keycloak"
    log_info "Exemple: $0 /home/<USER>/keycloak"
    exit 1
fi

# Vérifier si le répertoire themes existe
THEMES_PATH="$KEYCLOAK_PATH/themes"
if [ ! -d "$THEMES_PATH" ]; then
    log_error "Le répertoire themes '$THEMES_PATH' n'existe pas."
    log_info "Assurez-vous que '$KEYCLOAK_PATH' est bien le répertoire racine de Keycloak."
    exit 1
fi

# Vérifier si le thème workeem existe déjà
WORKEEM_THEME_PATH="$THEMES_PATH/workeem"
if [ -d "$WORKEEM_THEME_PATH" ]; then
    log_warning "Le thème Workeem existe déjà dans '$WORKEEM_THEME_PATH'"
    read -p "Voulez-vous le remplacer ? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Déploiement annulé."
        exit 0
    fi
    log_info "Suppression de l'ancien thème..."
    rm -rf "$WORKEEM_THEME_PATH"
fi

# Copier le thème
log_info "Copie du thème Workeem vers '$WORKEEM_THEME_PATH'..."
cp -r "$(dirname "$0")/workeem" "$THEMES_PATH/"

# Vérifier les permissions
log_info "Vérification des permissions..."
if [ -w "$THEMES_PATH" ]; then
    log_success "Permissions OK"
else
    log_warning "Permissions insuffisantes. Vous devrez peut-être exécuter avec sudo."
fi

# Vérifier la structure du thème
log_info "Vérification de la structure du thème..."
REQUIRED_FILES=(
    "$WORKEEM_THEME_PATH/login/theme.properties"
    "$WORKEEM_THEME_PATH/login/template.ftl"
    "$WORKEEM_THEME_PATH/login/login.ftl"
    "$WORKEEM_THEME_PATH/login/resources/css/workeem.css"
    "$WORKEEM_THEME_PATH/login/messages/messages_fr.properties"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        log_success "✓ $(basename "$file")"
    else
        log_error "✗ Fichier manquant: $file"
        exit 1
    fi
done

log_success "Thème Workeem déployé avec succès !"

# Instructions post-déploiement
echo
log_info "=== INSTRUCTIONS POST-DÉPLOIEMENT ==="
echo
log_info "1. Redémarrez Keycloak :"
echo "   systemctl restart keycloak"
echo "   # ou pour Docker :"
echo "   docker restart keycloak-container"
echo
log_info "2. Configurez le thème dans la console d'administration :"
echo "   - Connectez-vous à la console Keycloak"
echo "   - Sélectionnez votre realm"
echo "   - Allez dans Realm Settings > Themes"
echo "   - Sélectionnez 'workeem' dans Login Theme"
echo "   - Cliquez sur Save"
echo
log_info "3. Testez la page de connexion :"
echo "   - Ouvrez votre application"
echo "   - Déclenchez une connexion"
echo "   - Vérifiez que le thème Workeem s'affiche"
echo
log_success "Déploiement terminé !"
