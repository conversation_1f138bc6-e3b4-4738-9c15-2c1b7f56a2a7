:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Prévenir le scroll horizontal sur mobile */
* {
  box-sizing: border-box;
}

html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

.app-layout {
  height: 100vh;
  background-color: #F2F2F7;
  overflow-x: hidden;
  max-width: 100vw;
}

.menu-sidebar {
  position: fixed !important;
  left: 0;
  top: 0;
  z-index: 10;
  height: 100vh !important;
  background-color: #FFFFFF !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-right: 1px solid #E5E5EA;
  overflow-y: auto;
}

.header-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
  border: 1px solid transparent;
  color: #1C1C1E;
  font-size: 16px;
}

.header-trigger:hover {
  background: #F8F9FA;
  border-color: #E5E5EA;
  color: #6E56CF;
}

.trigger {
  font-size: 18px;
  transition: color 0.2s ease;
}

/* Logo Section */
.sidebar-logo {
  position: relative;
  height: 80px;
  padding: 16px 24px;
  overflow: hidden;
  background: #FFFFFF;
  border-bottom: 1px solid #E5E5EA;
  transition: all 0.3s ease;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.logo-image {
  height: 50px;
  width: auto;
  max-width: 220px;
  border-radius: 8px;
  object-fit: contain;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.logo-image.logo-collapsed {
  height: 48px;
  width: 48px;
  max-width: 48px;
}

/* Menu Styles */
.main-menu {
  border: none !important;
  background: transparent !important;
  padding: 16px 8px 100px 8px; /* Padding bottom pour le footer */
}

.menu-item {
  margin: 4px 8px !important;
  border-radius: 8px !important;
  height: 48px !important;
  line-height: 48px !important;
  color: #1C1C1E !important;
  font-weight: 500;
  transition: all 0.2s ease;
  padding: 0 16px !important;
  width: calc(100% - 16px) !important;
  display: flex !important;
  align-items: center !important;
}

.menu-item:hover {
  background-color: #EDE9F8 !important;
  color: #6E56CF !important;
}

.menu-item.ant-menu-item-selected {
  background-color: #6E56CF !important;
  color: #FFFFFF !important;
}

.menu-item .anticon {
  font-size: 18px;
  margin-right: 12px;
  min-width: 18px;
}

/* Sidebar Footer */
.sidebar-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  border-top: 1px solid #E5E5EA;
  background: #FFFFFF;
}

.sidebar-footer.collapsed {
  padding: 16px 8px;
}

.sidebar-footer.collapsed .tenant-info {
  justify-content: center;
  gap: 0;
}

.sidebar-footer.collapsed .tenant-details {
  display: none;
}

.sidebar-footer.collapsed .tenant-icon {
  margin: 0 auto;
}

.tenant-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tenant-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #EDE9F8;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6E56CF;
  font-size: 18px;
  flex-shrink: 0;
}

.tenant-details {
  flex: 1;
  min-width: 0;
}

.tenant-name {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  line-height: 1.2;
  margin-bottom: 2px;
}

.tenant-type {
  font-size: 12px;
  color: #8E8E93;
  line-height: 1.2;
}

/* Header Styles */
nz-header {
  position: fixed !important;
  top: 0;
  right: 0;
  padding: 0;
  width: calc(100% - 280px) !important;
  z-index: 9;
  background: #FFFFFF !important;
  border-bottom: 1px solid #E5E5EA;
  transition: width 0.2s ease;
}

/* Sites Panel Styles */
app-sites-panel {
  position: fixed !important;
  top: 64px;
  right: 0;
  width: calc(100% - 280px) !important;
  z-index: 8;
  transition: width 0.2s ease;
}

.app-header {
  position: relative;
  height: 64px;
  padding: 0 24px;
  background: #FFFFFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Menu de raccourcis - Style Apple */
.quick-actions-menu {
  position: relative;
}

.quick-actions-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  border: 1px solid rgba(0, 0, 0, 0.08);
  color: #1C1C1E;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.quick-actions-trigger:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.9) 100%);
  border-color: rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.quick-actions-trigger:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.quick-actions-dropdown {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 4px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(40px);
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  min-width: 420px;
  max-width: 420px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%) translateY(-10px) scale(0.95);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-actions-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0) scale(1);
}

.quick-actions-header {
  text-align: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.quick-actions-header h4 {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1C1C1E;
  letter-spacing: -0.01em;
}

.quick-actions-header p {
  margin: 0;
  font-size: 12px;
  color: #8E8E93;
  font-weight: 400;
  line-height: 1.3;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.quick-action-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 16px;
  background: rgba(248, 248, 248, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  gap: 12px;
}

.quick-action-item:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
  border-color: rgba(0, 0, 0, 0.08);
}

.quick-action-item:active {
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.action-icon {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.member-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.reservation-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
}

.space-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
}

.invoice-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  box-shadow: 0 4px 12px rgba(67, 233, 123, 0.3);
}

.subscription-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  box-shadow: 0 4px 12px rgba(250, 112, 154, 0.3);
}

.calendar-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  box-shadow: 0 4px 12px rgba(168, 237, 234, 0.3);
}

.action-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.action-label {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.action-description {
  font-size: 12px;
  font-weight: 400;
  color: #8E8E93;
  line-height: 1.2;
}

.quick-action-item:hover .action-icon {
  transform: scale(1.05);
}

/* Overlay pour fermer le menu en cliquant à l'extérieur */
.quick-actions-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
}

/* User Profile */
.user-profile {
  position: relative;
}

.user-info-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 20px;
  background: #F8F9FA;
  border: 1px solid #E5E5EA;
  transition: all 0.2s ease;
  min-width: 160px;
  cursor: pointer;
}

.user-info-container:hover {
  background-color: #F0F0F5;
  border-color: #6E56CF;
}

.user-avatar-img {
  flex-shrink: 0;
}

.dropdown-icon {
  font-size: 12px;
  color: #8E8E93;
  transition: transform 0.3s ease;
}

.user-info-container:hover .dropdown-icon {
  color: #6E56CF;
}

.user-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 12px;
  color: #8E8E93;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}



/* Content Styles */
nz-content {
  margin-left: 280px !important;
  margin-top: 110px !important; /* Ajusté pour la nouvelle taille du panel */
  padding: 24px;
  background: #F2F2F7 !important;
  min-height: calc(100vh - 110px); /* Ajusté pour le panel de sites */
  transition: margin-left 0.2s ease;
}

.inner-content {
  padding: 0;
  background: transparent;
  min-height: calc(100vh - 158px); /* Ajusté pour le panel de sites */
  padding-bottom: 24px;
}

/* Sidebar collapsed styles */
.menu-sidebar.ant-layout-sider-collapsed {
  width: 80px !important;
}

/* Header adjustment for collapsed sidebar */
.app-layout.sidebar-collapsed nz-header {
  width: calc(100% - 80px) !important;
}

.app-layout.sidebar-collapsed app-sites-panel {
  width: calc(100% - 80px) !important;
}

/* Ajuster le backdrop pour la sidebar collapsed */
.app-layout.sidebar-collapsed .sites-backdrop {
  left: 80px !important;
}

.app-layout.sidebar-collapsed nz-content {
  margin-left: 80px !important;
}

/* Mobile overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.45);
  z-index: 999;
  display: none;
}

/* Mobile layout adjustments */
.app-layout.mobile-layout .mobile-overlay {
  display: block;
}

/* Mobile/Desktop trigger visibility */
.mobile-trigger {
  display: none;
}

.desktop-trigger {
  display: flex;
}

/* Titre mobile caché par défaut */
.mobile-title {
  display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  /* Prévenir le scroll horizontal sur mobile */
  body, html {
    overflow-x: hidden !important;
    max-width: 100vw !important;
  }

  .app-layout {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    width: 100% !important;
  }

  /* Layout mobile spécifique */
  .app-layout.mobile-layout {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Force le header à aller jusqu'au bout sur mobile */
  .mobile-layout nz-header,
  .app-layout.mobile-layout nz-header {
    width: 100vw !important;
    max-width: 100vw !important;
    min-width: 100vw !important;
    left: 0 !important;
    right: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    height: 56px !important;
    min-height: 56px !important;
    max-height: 56px !important;
  }

  .mobile-layout .app-header,
  .app-layout.mobile-layout .app-header {
    width: 100vw !important;
    max-width: 100vw !important;
    min-width: 100vw !important;
    margin: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 56px !important;
    min-height: 56px !important;
    max-height: 56px !important;
    line-height: 56px !important;
  }

  /* Sites panel mobile */
  .mobile-layout app-sites-panel,
  .app-layout.mobile-layout app-sites-panel {
    width: 100vw !important;
    max-width: 100vw !important;
    left: 0 !important;
    right: 0 !important;
    top: 56px !important;
  }

  /* Force le contenu à utiliser toute la largeur sur mobile */
  .mobile-layout nz-content,
  .app-layout.mobile-layout nz-content {
    width: 100% !important;
    max-width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-top: 102px !important; /* Ajusté pour header + sites panel */
    padding: 16px !important;
    box-sizing: border-box !important;
    left: 0 !important;
    right: 0 !important;
  }

  .mobile-layout .inner-content,
  .app-layout.mobile-layout .inner-content {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
  }

  /* Mobile triggers */
  .mobile-trigger {
    display: flex !important;
  }

  .desktop-trigger {
    display: none !important;
  }

  /* Header responsive */
  .app-header {
    padding: 0 16px;
    height: 56px;
    width: 100vw !important;
    max-width: 100vw !important;
    min-width: 100vw !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    left: 0 !important;
    right: 0 !important;
  }

  .header-center {
    display: none !important; /* Cache les actions rapides sur mobile */
  }

  .header-right {
    gap: 4px;
  }

  .header-left {
    min-width: 40px;
  }

  /* Titre mobile */
  .mobile-title {
    display: flex !important;
    justify-content: center;
    align-items: center;
    flex: 1;
  }

  .page-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #1C1C1E;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }

  /* Sidebar mobile behavior */
  .menu-sidebar {
    position: fixed !important;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    width: 280px !important;
  }

  .menu-sidebar.ant-layout-sider-collapsed {
    transform: translateX(-100%);
    width: 280px !important;
  }

  .app-layout.sidebar-open .menu-sidebar {
    transform: translateX(0) !important;
  }

  /* Header adjustments */
  nz-header {
    width: 100vw !important;
    left: 0 !important;
    right: 0 !important;
    position: fixed !important;
    z-index: 100 !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    max-width: 100vw !important;
    min-width: 100vw !important;
  }

  /* Content adjustments */
  nz-content {
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-top: 56px !important;
    padding: 16px !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
    left: 0 !important;
    right: 0 !important;
  }

  .inner-content {
    padding: 0 !important;
    margin: 0 !important;
    max-width: 100% !important;
    width: 100% !important;
    box-sizing: border-box !important;
    overflow-x: hidden !important;
  }

  /* User profile responsive */
  .user-profile {
    min-width: auto;
    padding: 0;
  }

  .user-info-container {
    min-width: auto !important;
    padding: 4px 8px !important;
    gap: 6px !important;
  }

  .user-info {
    display: none !important;
  }

  .user-avatar-img {
    width: 32px !important;
    height: 32px !important;
    font-size: 14px;
  }

  .dropdown-icon {
    font-size: 12px !important;
  }

  /* Quick actions responsive */
  .quick-actions-dropdown {
    right: 12px;
    left: auto;
    width: calc(100vw - 24px);
    max-width: 320px;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .quick-action-item {
    padding: 12px;
  }

  .action-content {
    margin-left: 8px;
  }

  .action-label {
    font-size: 14px;
  }

  .action-description {
    font-size: 12px;
  }
}

/* Menu dropdown utilisateur */
.ant-dropdown-menu-item {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 8px 16px !important;
  font-size: 14px !important;
}

.ant-dropdown-menu-item:hover {
  background-color: #F5F5F7 !important;
}

.logout-item {
  color: #FF3B30 !important;
}

.logout-item:hover {
  background-color: #FFF5F5 !important;
  color: #FF3B30 !important;
}

.logout-item nz-icon {
  color: #FF3B30 !important;
}

/* Sidebar footer responsive */
@media (max-width: 768px) {
  .sidebar-footer {
    padding: 12px !important;
  }

  .tenant-info {
    justify-content: center !important;
    gap: 0 !important;
  }

  .tenant-details {
    display: none !important;
  }

  .tenant-icon {
    margin: 0 auto !important;
  }
}
