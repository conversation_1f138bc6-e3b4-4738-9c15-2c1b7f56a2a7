export interface Member {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  memberType: MemberType;
  subscriptionType: string;
  status: MemberStatus;
  studentCode?: string; // Uniquement pour les étudiants
  createdAt: Date;
  updatedAt: Date;
  profileImage?: string;
}

export enum MemberType {
  STUDENT = 'student',
  PROFESSIONAL = 'professional'
}

export enum MemberStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

export interface MemberHistory {
  id: string;
  memberId: string;
  type: HistoryType;
  description: string;
  amount?: number;
  date: Date;
  details?: any;
}

export enum HistoryType {
  RESERVATION = 'reservation',
  PAYMENT = 'payment',
  SUBSCRIPTION_CHANGE = 'subscription_change',
  STATUS_CHANGE = 'status_change'
}

export interface Subscription {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number; // en jours
  features: string[];
  isActive: boolean;
  memberType?: MemberType; // Si spécifique à un type
}

export interface CreateMemberRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  memberType: MemberType;
  subscriptionId: string;
  studentCode?: string;
}

export interface UpdateMemberRequest {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  subscriptionId?: string;
  status?: MemberStatus;
  studentCode?: string;
}
