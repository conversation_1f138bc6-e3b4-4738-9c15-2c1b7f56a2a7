export interface Space {
  id: string;
  name: string;
  description: string;
  type: SpaceType;
  capacity: number;
  location: string;
  floor?: string;
  area: number; // en m²
  status: SpaceStatus;
  equipment: Equipment[];
  amenities: string[];
  images: string[];
  availability: SpaceAvailability;
  pricing: SpacePricing;
  rules: string[];
  createdAt: Date;
  updatedAt: Date;
}

export enum SpaceType {
  WORKSTATION = 'WORKSTATION',        // Poste de travail
  PRIVATE_OFFICE = 'PRIVATE_OFFICE',   // Bureau privé
  MEETING_ROOM = 'MEETING_ROOM',       // Salle de réunion
  PHONE_BOOTH = 'PHONE_BOOTH',         // Cabine téléphonique
  LOUNGE = 'LOUNGE',                   // Espace détente
  CONFERENCE_ROOM = 'CONFERENCE_ROOM', // Salle de conférence
  HOT_DESK = 'HOT_DESK',              // Bureau partagé
  DEDICATED_DESK = 'DEDICATED_DESK',   // Bureau dédié
  COLLABORATIVE = 'COLLABORATIVE',     // Espace collaboratif
  EVENT_SPACE = 'EVENT_SPACE'          // Espace événementiel
}

export enum SpaceStatus {
  AVAILABLE = 'AVAILABLE',
  OCCUPIED = 'OCCUPIED',
  MAINTENANCE = 'MAINTENANCE',
  OUT_OF_ORDER = 'OUT_OF_ORDER',
  RESERVED = 'RESERVED'
}

export interface Equipment {
  id: string;
  name: string;
  type: EquipmentType;
  brand?: string;
  model?: string;
  quantity: number;
  status: EquipmentStatus;
  description?: string;
}

export enum EquipmentType {
  COMPUTER = 'COMPUTER',
  MONITOR = 'MONITOR',
  PRINTER = 'PRINTER',
  PROJECTOR = 'PROJECTOR',
  WHITEBOARD = 'WHITEBOARD',
  TV_SCREEN = 'TV_SCREEN',
  PHONE = 'PHONE',
  WEBCAM = 'WEBCAM',
  MICROPHONE = 'MICROPHONE',
  SPEAKERS = 'SPEAKERS',
  DESK = 'DESK',
  CHAIR = 'CHAIR',
  STORAGE = 'STORAGE',
  WIFI = 'WIFI',
  ETHERNET = 'ETHERNET'
}

export enum EquipmentStatus {
  WORKING = 'WORKING',
  BROKEN = 'BROKEN',
  MAINTENANCE = 'MAINTENANCE'
}

export interface SpaceAvailability {
  isActive: boolean;
  schedule: WeeklySchedule;
  exceptions: DateException[];
  advanceBookingDays: number;
  minBookingDuration: number; // en minutes
  maxBookingDuration: number; // en minutes
  bufferTime: number; // temps entre réservations en minutes
}

export interface WeeklySchedule {
  monday: DaySchedule;
  tuesday: DaySchedule;
  wednesday: DaySchedule;
  thursday: DaySchedule;
  friday: DaySchedule;
  saturday: DaySchedule;
  sunday: DaySchedule;
}

export interface DaySchedule {
  isOpen: boolean;
  openTime: string; // format HH:mm
  closeTime: string; // format HH:mm
  breaks: TimeSlot[];
}

export interface TimeSlot {
  start: string; // format HH:mm
  end: string; // format HH:mm
  reason?: string;
}

export interface DateException {
  date: Date;
  type: ExceptionType;
  reason: string;
  customSchedule?: DaySchedule;
}

export enum ExceptionType {
  CLOSED = 'closed',
  CUSTOM_HOURS = 'custom_hours',
  MAINTENANCE = 'maintenance',
  EVENT = 'event'
}

export interface SpacePricing {
  hourlyRate: number;
  dailyRate: number;
  weeklyRate: number;
  monthlyRate: number;
  currency: string;
  discounts: PricingDiscount[];
}

export interface PricingDiscount {
  type: DiscountType;
  value: number; // pourcentage ou montant fixe
  isPercentage: boolean;
  conditions: string;
  validFrom?: Date;
  validTo?: Date;
}

export enum DiscountType {
  BULK_BOOKING = 'bulk_booking',
  LONG_TERM = 'long_term',
  MEMBER_DISCOUNT = 'member_discount',
  EARLY_BIRD = 'early_bird',
  OFF_PEAK = 'off_peak'
}

// Interfaces pour les requêtes API
export interface CreateSpaceRequest {
  name: string;
  description: string;
  type: SpaceType;
  capacity: number;
  location: string;
  floor?: string;
  area: number;
  equipment: Equipment[];
  amenities: string[];
  availability: SpaceAvailability;
  pricing: SpacePricing;
  rules: string[];
}

export interface UpdateSpaceRequest extends Partial<CreateSpaceRequest> {
  id: string;
}

export interface SpaceSearchFilters {
  type?: SpaceType;
  capacity?: number;
  location?: string;
  floor?: string;
  equipment?: EquipmentType[];
  amenities?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  availability?: {
    date: Date;
    startTime: string;
    endTime: string;
  };
}

// Interface pour les réservations d'espaces
export interface SpaceReservation {
  id: string;
  spaceId: string;
  spaceName: string;
  userId: string;
  userName: string;
  startTime: Date;
  endTime: Date;
  status: ReservationStatus;
  purpose: string;
  attendees: number;
  notes?: string;
  totalCost: number;
  createdAt: Date;
  updatedAt: Date;
}

export enum ReservationStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  NO_SHOW = 'no_show'
}

// Interface pour le planning visuel
export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  resourceId: string; // spaceId
  color?: string;
  status: ReservationStatus;
  attendees?: number;
  userName?: string;
}

export interface CalendarResource {
  id: string;
  title: string;
  type: SpaceType;
  capacity: number;
  location: string;
  status: SpaceStatus;
}
