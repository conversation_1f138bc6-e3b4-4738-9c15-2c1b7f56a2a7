export interface Reservation {
  id: string;
  spaceId: string;
  spaceName: string;
  userId: string;
  userName: string;
  userEmail: string;
  startTime: Date;
  endTime: Date;
  purpose?: string; // Rendu optionnel
  status: ReservationStatus;
  numberOfPeople?: number;
  notes?: string;
  recurrence?: RecurrenceType;
  createdAt: Date;
  updatedAt: Date;
}

export type ReservationStatus = 'pending' | 'confirmed' | 'cancelled' | 'completed';

export type RecurrenceType = 'none' | 'daily' | 'weekly' | 'monthly';

export interface ReservationFilter {
  status?: ReservationStatus;
  spaceId?: string;
  userId?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  extendedProps?: {
    reservation: Reservation;
    status: ReservationStatus;
  };
}
