<div class="reservation-form-container">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
        {{ isEditMode ? 'Modifier la réservation' : 'Nouvelle réservation' }}
      </h1>
      <p
        class="page-description">{{ isEditMode ? 'Modifiez les détails de votre réservation' : 'Réservez un espace pour votre activité' }}</p>
    </div>
    <div class="header-actions">
      <button nz-button nzType="default" nzSize="large" (click)="onCancel()" class="cancel-button">
        <nz-icon nzType="arrow-left"></nz-icon>
        Retour
      </button>
    </div>
  </div>

  <!-- Formulaire -->
  <nz-card class="form-card" nzTitle="Détails de la réservation">
    <form nz-form [formGroup]="reservationForm" (ngSubmit)="onSubmit()" class="reservation-form">
      <div nz-row [nzGutter]="16">
        <!-- Sélection de l'espace -->
        <div nz-col nzSpan="24">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Espace</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner un espace">
              <nz-select
                formControlName="spaceId"
                nzPlaceHolder="Sélectionner un espace"
                nzShowSearch
                (ngModelChange)="onSpaceChange($event)"
              >
                <nz-option
                  *ngFor="let space of spaces"
                  [nzLabel]="space.name"
                  [nzValue]="space.id"
                >
                  <div class="space-option">
                    <div class="space-name">{{ space.name }}</div>
                    <div class="space-details">
                      {{ space.location }} • {{ space.capacity }} personnes
                    </div>
                  </div>
                </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Sélection du client -->
      <div nz-row [nzGutter]="16">
        <div nz-col nzSpan="24">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Client</nz-form-label>
            <nz-form-control>
              <nz-radio-group formControlName="clientType">
                <label nz-radio nzValue="existing">Client existant</label>
                <label nz-radio nzValue="new">Nouveau client</label>
              </nz-radio-group>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Client existant -->
      <div nz-row [nzGutter]="16" *ngIf="reservationForm.get('clientType')?.value === 'existing'">
        <div nz-col nzSpan="24">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Sélectionner un client</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner un client">
              <nz-select
                formControlName="existingClientId"
                nzPlaceHolder="Rechercher un client..."
                nzShowSearch
                [nzFilterOption]="filterMemberOption"
                style="width: 100%"
              >
                <nz-option
                  *ngFor="let member of members"
                  [nzLabel]="member.firstName + ' ' + member.lastName + ' (' + member.email + ')'"
                  [nzValue]="member.id"
                >
                  <div class="member-option">
                    <div class="member-name">{{ member.firstName }} {{ member.lastName }}</div>
                    <div class="member-details">
                      {{ member.email }} • {{ member.company }} • {{ member.subscriptionType }}
                    </div>
                  </div>
                </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Nouveau client -->
      <div *ngIf="reservationForm.get('clientType')?.value === 'new'" class="new-client-section">
        <nz-card class="new-client-card" nzTitle="Informations du nouveau client">
          <div class="new-client-form">

        <div nz-row [nzGutter]="16">
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label [nzRequired]="true">Prénom</nz-form-label>
              <nz-form-control nzErrorTip="Veuillez saisir le prénom">
                <input nz-input formControlName="firstName" placeholder="Prénom"/>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label [nzRequired]="true">Nom</nz-form-label>
              <nz-form-control nzErrorTip="Veuillez saisir le nom">
                <input nz-input formControlName="lastName" placeholder="Nom"/>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <div nz-row [nzGutter]="16">
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label [nzRequired]="false">Email</nz-form-label>
              <nz-form-control>
                <input nz-input formControlName="email" placeholder="<EMAIL>" type="email"/>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label [nzRequired]="true">Téléphone</nz-form-label>
              <nz-form-control nzErrorTip="Veuillez saisir un numéro de téléphone">
                <input nz-input formControlName="phone" placeholder="+33 1 23 45 67 89"/>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <div nz-row [nzGutter]="16">
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label>Entreprise</nz-form-label>
              <nz-form-control>
                <input nz-input formControlName="company" placeholder="Nom de l'entreprise"/>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label>Type</nz-form-label>
              <nz-form-control>
                <nz-select formControlName="studentType" nzPlaceHolder="Sélectionner le type">
                  <nz-option nzLabel="Étudiant" nzValue="Étudiant"></nz-option>
                  <nz-option nzLabel="Professionnel" nzValue="Professionnel"></nz-option>
                  <nz-option nzLabel="Particulier" nzValue="Particulier"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <div nz-row [nzGutter]="16">
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label>Abonnement</nz-form-label>
              <nz-form-control>
                <nz-select formControlName="subscriptionType" nzPlaceHolder="Type d'abonnement">
                  <nz-option nzLabel="Basic" nzValue="Basic"></nz-option>
                  <nz-option nzLabel="Standard" nzValue="Standard"></nz-option>
                  <nz-option nzLabel="Premium" nzValue="Premium"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>
          </div>
        </nz-card>
      </div>

      <div nz-row [nzGutter]="16">
        <!-- Date de début -->
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Date de début</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner une date de début">
              <nz-date-picker
                formControlName="startDate"
                nzPlaceHolder="Sélectionner la date de début"
                [nzDisabledDate]="disabledDate"
                (ngModelChange)="calculateDurationFromDates()"
                style="width: 100%"
              ></nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- Date de fin -->
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Date de fin</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner une date de fin">
              <nz-date-picker
                formControlName="endDate"
                nzPlaceHolder="Sélectionner la date de fin"
                [nzDisabledDate]="disabledEndDate"
                (ngModelChange)="calculateDurationFromDates()"
                style="width: 100%"
              ></nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Heures et nombre de personnes -->
      <div nz-row [nzGutter]="16">
        <!-- Heure de début -->
        <div nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Heure de début</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner l'heure de début">
              <nz-time-picker
                formControlName="startTime"
                nzFormat="HH:mm"
                nzPlaceHolder="Sélectionner l'heure"
                [nzDisabledHours]="disabledHours"
                [nzDisabledMinutes]="disabledMinutes"
                style="width: 100%"
              ></nz-time-picker>
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- Heure de fin -->
        <div nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Heure de fin</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner l'heure de fin">
              <nz-time-picker
                formControlName="endTime"
                nzFormat="HH:mm"
                nzPlaceHolder="Sélectionner l'heure de fin"
                (ngModelChange)="onEndTimeChange()"
                style="width: 100%"
              ></nz-time-picker>
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- Nombre de personnes -->
        <div nz-col nzSpan="8">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Nombre de personnes</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez indiquer le nombre de personnes">
              <nz-input-number
                formControlName="numberOfPeople"
                [nzMin]="1"
                [nzMax]="selectedSpace?.capacity || 50"
                nzPlaceHolder="Nombre de personnes"
                style="width: 100%"
              ></nz-input-number>
              <div class="capacity-info" *ngIf="selectedSpace">
                <small>Capacité maximale : {{ selectedSpace.capacity }} personnes</small>
              </div>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Durée calculée -->
      <div nz-row [nzGutter]="16" *ngIf="getDurationText()">
        <div nz-col nzSpan="24">
          <nz-alert
            nzType="info"
            [nzMessage]="getDurationText()"
            nzShowIcon
            style="margin-bottom: 16px;"
          ></nz-alert>
        </div>
      </div>

      <!-- Durée -->
      <div nz-row [nzGutter]="16">
        <div nz-col nzSpan="24">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Durée</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner une durée valide">
              <div class="duration-inputs">
                <div class="duration-group">
                  <nz-input-number
                    formControlName="durationMonths"
                    [nzMin]="0"
                    [nzMax]="12"
                    [nzStep]="1"
                    nzPlaceHolder="0"
                    [nzDisabled]="true"
                    class="duration-input"
                  ></nz-input-number>
                  <span class="duration-label">mois</span>
                </div>

                <div class="duration-group">
                  <nz-input-number
                    formControlName="durationDays"
                    [nzMin]="0"
                    [nzMax]="31"
                    [nzStep]="1"
                    nzPlaceHolder="0"
                    [nzDisabled]="true"
                    class="duration-input"
                  ></nz-input-number>
                  <span class="duration-label">jours</span>
                </div>

                <div class="duration-group">
                  <nz-input-number
                    formControlName="durationHours"
                    [nzMin]="0"
                    [nzMax]="23"
                    [nzStep]="1"
                    nzPlaceHolder="0"
                    [nzDisabled]="true"
                    class="duration-input"
                  ></nz-input-number>
                  <span class="duration-label">heures</span>
                </div>

                <div class="duration-group">
                  <nz-select
                    formControlName="durationMinutes"
                    nzPlaceHolder="0"
                    [nzDisabled]="true"
                    class="duration-select"
                  >
                    <nz-option nzLabel="0 min" [nzValue]="0"></nz-option>
                    <nz-option nzLabel="15 min" [nzValue]="15"></nz-option>
                    <nz-option nzLabel="30 min" [nzValue]="30"></nz-option>
                    <nz-option nzLabel="45 min" [nzValue]="45"></nz-option>
                  </nz-select>
                </div>

                <div class="duration-total" *ngIf="getTotalDurationText()">
                  <span class="total-label">Durée calculée: {{ getTotalDurationText() }}</span>
                </div>
              </div>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>



        <!-- Objet et récurrence -->
        <div nz-row [nzGutter]="16">
          <div nz-col nzSpan="16">
            <nz-form-item>
              <nz-form-label [nzRequired]="false">Objet de la réservation (optionnel)</nz-form-label>
              <nz-form-control>
                <input
                  nz-input
                  formControlName="purpose"
                  placeholder="Ex: Réunion équipe, Formation, Entretien client..."
                />
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label>Récurrence</nz-form-label>
              <nz-form-control>
                <nz-select
                  formControlName="recurrence"
                  nzPlaceHolder="Aucune"
                  nzAllowClear
                >
                  <nz-option nzLabel="Aucune" nzValue="none"></nz-option>
                  <nz-option nzLabel="Quotidienne" nzValue="daily"></nz-option>
                  <nz-option nzLabel="Hebdomadaire" nzValue="weekly"></nz-option>
                  <nz-option nzLabel="Mensuelle" nzValue="monthly"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <!-- Notes additionnelles -->
        <div nz-row [nzGutter]="16">
          <div nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label>Notes additionnelles</nz-form-label>
              <nz-form-control>
                <textarea
                  nz-input
                  formControlName="notes"
                  placeholder="Informations complémentaires, besoins spécifiques..."
                  rows="3"
                ></textarea>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>



        <!-- Informations sur l'espace sélectionné -->
        <nz-card
          *ngIf="selectedSpace"
          class="space-info-card"
          nzTitle="Informations sur l'espace"
          nzSize="small"
        >
          <div class="space-info">
            <div class="info-item">
              <nz-icon nzType="environment" nzTheme="outline"></nz-icon>
              <span>{{ selectedSpace.location }}</span>
            </div>
            <div class="info-item">
              <nz-icon nzType="team" nzTheme="outline"></nz-icon>
              <span>{{ selectedSpace.capacity }} personnes</span>
            </div>
            <div class="info-item">
              <nz-icon nzType="dollar" nzTheme="outline"></nz-icon>
              <span>{{ selectedSpace.pricing.hourlyRate }}€/h</span>
            </div>
          </div>

          <!-- Équipements disponibles -->
          <div class="equipment-list" *ngIf="selectedSpace.equipment.length > 0">
            <h4>Équipements disponibles :</h4>
            <nz-tag
              *ngFor="let equipment of selectedSpace.equipment"
              [nzColor]="equipment.status === EquipmentStatus.WORKING ? 'green' : 'orange'"
            >
              {{ equipment.name }}
            </nz-tag>
          </div>
        </nz-card>

        <!-- Actions -->
        <div class="form-actions">
          <button nz-button nzType="default" (click)="onCancel()">
            Annuler
          </button>
          <button
            nz-button
            nzType="primary"
            nzHtmlType="submit"
            [nzLoading]="saving"
            [disabled]="reservationForm.invalid"
          >
            {{ isEditMode ? 'Modifier' : 'Réserver' }}
          </button>
        </div>
    </form>
  </nz-card>
</div>
