import { Component, OnInit, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageModule } from 'ng-zorro-antd/message';

import { MemberService } from '../../services/member.service';
import { Member, MemberType, MemberStatus, Subscription, CreateMemberRequest, UpdateMemberRequest } from '../../models/member.model';

@Component({
  selector: 'app-members',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzTableModule,
    NzButtonModule,
    NzIconModule,
    NzInputModule,
    NzSelectModule,
    NzModalModule,
    NzFormModule,
    NzTagModule,
    NzCardModule,
    NzSpinModule,
    NzToolTipModule,
    NzDropDownModule,
    NzModalModule,
    NzMessageModule
  ],
  templateUrl: './members.component.html',
  styleUrl: './members.component.css'
})
export class MembersComponent implements OnInit {
  // Signaux pour la gestion d'état
  private membersSignal = signal<Member[]>([]);
  private subscriptionsSignal = signal<Subscription[]>([]);
  private searchTermSignal = signal<string>('');
  private selectedStatusSignal = signal<MemberStatus | 'all'>('all');
  private selectedTypeSignal = signal<MemberType | 'all'>('all');
  private selectedSubscriptionSignal = signal<string | 'all'>('all');

  // Configuration des colonnes pour le tri
  listOfColumn = [
    {
      title: 'Membre',
      compare: (a: Member, b: Member) => a.firstName.localeCompare(b.firstName),
      priority: false
    },
    {
      title: 'Contact',
      compare: (a: Member, b: Member) => a.email.localeCompare(b.email),
      priority: false
    },
    {
      title: 'Type',
      compare: (a: Member, b: Member) => a.memberType.localeCompare(b.memberType),
      priority: false
    },
    {
      title: 'Abonnement',
      compare: (a: Member, b: Member) => a.subscriptionType.localeCompare(b.subscriptionType),
      priority: false
    },
    {
      title: 'Statut',
      compare: (a: Member, b: Member) => a.status.localeCompare(b.status),
      priority: false
    },
    {
      title: 'Code étudiant',
      compare: (a: Member, b: Member) => (a.studentCode || '').localeCompare(b.studentCode || ''),
      priority: false
    },
    {
      title: 'Date création',
      compare: (a: Member, b: Member) => {
        const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
        const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
        return dateA.getTime() - dateB.getTime();
      },
      priority: false
    }
  ];

  // Signaux calculés (filtrage seulement, le tri est géré par ng-zorro)
  filteredMembers = computed(() => {
    const members = this.membersSignal();
    const searchTerm = this.searchTermSignal().toLowerCase();
    const selectedStatus = this.selectedStatusSignal();
    const selectedType = this.selectedTypeSignal();
    const selectedSubscription = this.selectedSubscriptionSignal();

    return members.filter(member => {
      const matchesSearch = !searchTerm ||
        member.firstName.toLowerCase().includes(searchTerm) ||
        member.lastName.toLowerCase().includes(searchTerm) ||
        member.email.toLowerCase().includes(searchTerm) ||
        (member.studentCode && member.studentCode.toLowerCase().includes(searchTerm));

      const matchesStatus = selectedStatus === 'all' || member.status === selectedStatus;
      const matchesType = selectedType === 'all' || member.memberType === selectedType;
      const matchesSubscription = selectedSubscription === 'all' || member.subscriptionType === selectedSubscription;

      return matchesSearch && matchesStatus && matchesType && matchesSubscription;
    });
  });

  // Propriétés pour les modales (propriétés normales pour le binding bidirectionnel)
  isCreateModalVisible = false;
  isEditModalVisible = false;
  selectedMember: Member | null = null;

  // Formulaires
  createForm: FormGroup;
  editForm: FormGroup;

  // Enums pour les templates
  MemberType = MemberType;
  MemberStatus = MemberStatus;

  // Options pour les selects
  statusOptions = [
    { label: 'Tous', value: 'all' },
    { label: 'Actif', value: MemberStatus.ACTIVE },
    { label: 'Inactif', value: MemberStatus.INACTIVE },
    { label: 'Suspendu', value: MemberStatus.SUSPENDED }
  ];

  typeOptions = [
    { label: 'Tous', value: 'all' },
    { label: 'Étudiant', value: MemberType.STUDENT },
    { label: 'Professionnel', value: MemberType.PROFESSIONAL }
  ];

  constructor(
    private memberService: MemberService,
    private fb: FormBuilder,
    private message: NzMessageService,
    private router: Router,
    private modal: NzModalService
  ) {
    this.createForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^(\+33|0)[1-9](\d{8})$/)]],
      memberType: [MemberType.STUDENT, [Validators.required]],
      subscriptionId: ['', [Validators.required]],
      studentCode: ['']
    });

    this.editForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^(\+33|0)[1-9](\d{8})$/)]],
      subscriptionId: ['', [Validators.required]],
      status: ['', [Validators.required]],
      studentCode: ['']
    });

    // Surveiller les changements de type de membre pour afficher/masquer le code étudiant
    this.createForm.get('memberType')?.valueChanges.subscribe(type => {
      const studentCodeControl = this.createForm.get('studentCode');
      if (type === MemberType.STUDENT) {
        studentCodeControl?.setValidators([Validators.required]);
      } else {
        studentCodeControl?.clearValidators();
        studentCodeControl?.setValue('');
      }
      studentCodeControl?.updateValueAndValidity();
    });
  }

  ngOnInit() {
    this.loadMembers();
    this.loadSubscriptions();
  }

  // Chargement des données
  loadMembers() {
    this.memberService.getMembers().subscribe(members => {
      this.membersSignal.set(members);
    });
  }

  loadSubscriptions() {
    this.memberService.getSubscriptions().subscribe(subscriptions => {
      this.subscriptionsSignal.set(subscriptions);
    });
  }

  // Getters pour les signaux (pour les templates)
  get members() { return this.membersSignal(); }
  get subscriptions() { return this.subscriptionsSignal(); }
  get loading() { return this.memberService.loading(); }

  // Méthodes pour les templates (pour éviter les erreurs de binding)
  isLoading() { return this.memberService.loading(); }
  getFilteredMembers() { return this.filteredMembers(); }

  // Filtres
  onSearchChange(searchTerm: string) {
    this.searchTermSignal.set(searchTerm);
  }

  onStatusFilterChange(status: MemberStatus | 'all') {
    this.selectedStatusSignal.set(status);
  }

  onTypeFilterChange(type: MemberType | 'all') {
    this.selectedTypeSignal.set(type);
  }

  onSubscriptionFilterChange(subscription: string | 'all') {
    this.selectedSubscriptionSignal.set(subscription);
  }

  // Effacer tous les filtres
  clearFilters() {
    this.searchTermSignal.set('');
    this.selectedStatusSignal.set('all');
    this.selectedTypeSignal.set('all');
    this.selectedSubscriptionSignal.set('all');

    // Réinitialiser le champ de recherche dans le DOM
    const searchInput = document.querySelector('.filters-card input[nz-input]') as HTMLInputElement;
    if (searchInput) {
      searchInput.value = '';
    }
  }



  // Navigation vers les formulaires
  navigateToCreate() {
    this.router.navigate(['/members/new']);
  }

  navigateToEdit(member: Member) {
    this.router.navigate(['/members/edit', member.id]);
  }

  showMemberDetails(member: Member) {
    this.router.navigate(['/members/details', member.id]);
  }



  // CRUD Operations
  createMember() {
    if (this.createForm.valid) {
      const formValue = this.createForm.value;
      const createRequest: CreateMemberRequest = {
        firstName: formValue.firstName,
        lastName: formValue.lastName,
        email: formValue.email,
        phone: formValue.phone,
        memberType: formValue.memberType,
        subscriptionId: formValue.subscriptionId,
        studentCode: formValue.memberType === MemberType.STUDENT ? formValue.studentCode : undefined
      };

      this.memberService.createMember(createRequest).subscribe({
        next: (member) => {
          this.message.success('Membre créé avec succès');
          this.loadMembers();
        },
        error: (error) => {
          this.message.error('Erreur lors de la création du membre');
        }
      });
    } else {
      Object.values(this.createForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  updateMember() {
    if (this.editForm.valid && this.selectedMember) {
      const formValue = this.editForm.value;
      const updateRequest: UpdateMemberRequest = {
        firstName: formValue.firstName,
        lastName: formValue.lastName,
        email: formValue.email,
        phone: formValue.phone,
        subscriptionId: formValue.subscriptionId,
        status: formValue.status,
        studentCode: formValue.studentCode || undefined
      };

      this.memberService.updateMember(this.selectedMember.id, updateRequest).subscribe({
        next: (member) => {
          this.message.success('Membre mis à jour avec succès');
          this.loadMembers();
        },
        error: (error) => {
          this.message.error('Erreur lors de la mise à jour du membre');
        }
      });
    }
  }

  confirmDeleteMember(member: Member) {
    this.modal.confirm({
      nzTitle: 'Supprimer le membre',
      nzContent: `Êtes-vous sûr de vouloir supprimer le membre <strong>${member.firstName} ${member.lastName}</strong> ?<br><br>Cette action est irréversible.`,
      nzOkText: 'Supprimer',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: 'Annuler',
      nzCentered: true,
      nzOnOk: () => {
        this.deleteMember(member);
      }
    });
  }

  private deleteMember(member: Member) {
    this.memberService.deleteMember(member.id).subscribe({
      next: () => {
        this.message.success('Membre supprimé avec succès');
        this.loadMembers();
      },
      error: (error) => {
        this.message.error('Erreur lors de la suppression du membre');
      }
    });
  }

  // Utilitaires
  getStatusColor(status: MemberStatus): string {
    switch (status) {
      case MemberStatus.ACTIVE: return 'green';
      case MemberStatus.INACTIVE: return 'orange';
      case MemberStatus.SUSPENDED: return 'red';
      default: return 'default';
    }
  }

  getFormattedDate(date: Date | string | undefined): string {
    if (!date) return '-';

    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return '-';

      return dateObj.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '-';
    }
  }

  getStatusText(status: MemberStatus): string {
    switch (status) {
      case MemberStatus.ACTIVE: return 'Actif';
      case MemberStatus.INACTIVE: return 'Inactif';
      case MemberStatus.SUSPENDED: return 'Suspendu';
      default: return status;
    }
  }

  getTypeText(type: MemberType): string {
    return type === MemberType.STUDENT ? 'Étudiant' : 'Professionnel';
  }

  getTypeColor(type: MemberType): string {
    return type === MemberType.STUDENT ? 'blue' : 'purple';
  }

  // Obtenir les abonnements filtrés par type
  getSubscriptionsByType(memberType: MemberType) {
    return this.subscriptions.filter(s => !s.memberType || s.memberType === memberType);
  }


}
