<div class="billing-details-container">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <div class="title-section">
        <h1 class="page-title">
          <nz-icon nzType="file-text" nzTheme="outline"></nz-icon>
          Facture {{ invoice?.invoiceNumber }}
        </h1>
        <p class="page-description">Détails de la facture et historique des paiements</p>
      </div>
    </div>
    <div class="header-actions">
      <button nz-button nzType="text" nzSize="large" (click)="goBack()" class="back-button">
        <nz-icon nzType="arrow-left"></nz-icon>
        Retour
      </button>
      <button nz-button nzType="default" nzSize="large" (click)="downloadPDF()" class="action-button">
        <nz-icon nzType="download"></nz-icon>
        Télécharger PDF
      </button>
      <button nz-button nzType="default" nzSize="large" nz-dropdown [nzDropdownMenu]="shareMenu" nzPlacement="bottomLeft" class="action-button">
        <nz-icon nzType="share-alt"></nz-icon>
        Partager
      </button>
      <nz-dropdown-menu #shareMenu="nzDropdownMenu">
        <ul nz-menu>
          <li nz-menu-item (click)="shareViaLink()">
            <nz-icon nzType="link" nzTheme="outline"></nz-icon>
            Lien de partage
          </li>
          <li nz-menu-divider></li>
          <li nz-menu-item (click)="shareViaWhatsApp()">
            <nz-icon nzType="whats-app" nzTheme="outline"></nz-icon>
            WhatsApp
          </li>
          <li nz-menu-item (click)="shareViaEmail()">
            <nz-icon nzType="mail" nzTheme="outline"></nz-icon>
            Email
          </li>
        </ul>
      </nz-dropdown-menu>
      <button
        nz-button
        nzType="primary"
        nzSize="large"
        (click)="markAsPaid()"
        [disabled]="invoice?.status === 'paid'"
        class="action-button"
      >
        <nz-icon nzType="check-circle"></nz-icon>
        {{ invoice?.status === 'paid' ? 'Payée' : 'Marquer comme payée' }}
      </button>
    </div>
  </div>

  <div class="content-grid" *ngIf="invoice">
    <!-- Informations de la facture -->
    <nz-card class="invoice-card" nzTitle="Informations de la facture">
      <div class="invoice-info">
        <div class="info-row">
          <div class="info-item">
            <label>Numéro de facture</label>
            <span class="value">{{ invoice.invoiceNumber }}</span>
          </div>
          <div class="info-item">
            <label>Statut</label>
            <nz-tag [nzColor]="getStatusColor(invoice.status)" class="status-tag">
              {{ getStatusLabel(invoice.status) }}
            </nz-tag>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <label>Date d'émission</label>
            <span class="value">{{ invoice.issueDate | date:'dd/MM/yyyy' }}</span>
          </div>
          <div class="info-item">
            <label>Date d'échéance</label>
            <span class="value">{{ invoice.dueDate | date:'dd/MM/yyyy' }}</span>
          </div>
        </div>

        <div class="info-row" *ngIf="invoice.paidDate">
          <div class="info-item">
            <label>Date de paiement</label>
            <span class="value">{{ invoice.paidDate | date:'dd/MM/yyyy' }}</span>
          </div>
          <div class="info-item">
            <label>Méthode de paiement</label>
            <span class="value">{{ getPaymentMethodLabel(invoice.paymentMethod) }}</span>
          </div>
        </div>
      </div>
    </nz-card>

    <!-- Informations du client -->
    <nz-card class="client-card" nzTitle="Informations du client">
      <div class="client-info">
        <div class="client-header">
          <div class="client-avatar">
            <nz-avatar [nzSize]="64" [nzText]="getClientInitials()" nzIcon="user"></nz-avatar>
          </div>
          <div class="client-details">
            <h3 class="client-name" (click)="navigateToClient()">{{ invoice.memberName }}</h3>
            <p class="client-email">{{ invoice.memberEmail }}</p>
            <p class="client-company" *ngIf="invoice.memberCompany">{{ invoice.memberCompany }}</p>
          </div>
        </div>
      </div>
    </nz-card>

    <!-- Détails de la facture -->
    <nz-card class="items-card" nzTitle="Détails de la facture">
      <nz-table [nzData]="invoice.items" [nzShowPagination]="false">
        <thead>
          <tr>
            <th>Description</th>
            <th>Période</th>
            <th>Quantité</th>
            <th>Prix unitaire</th>
            <th>Total</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of invoice.items">
            <td>
              <div class="item-description">
                <strong>{{ item.description }}</strong>
                <div class="item-type">{{ getItemTypeLabel(item.type) }}</div>
              </div>
            </td>
            <td>
              <span *ngIf="item.period">
                {{ getFormattedDate(item.period.start) }} - {{ getFormattedDate(item.period.end) }}
              </span>
              <span *ngIf="!item.period">-</span>
            </td>
            <td>{{ item.quantity }}</td>
            <td>{{ item.unitPrice | currency:'MAD ':'symbol':'1.2-2' }}</td>
            <td><strong>{{ item.total | currency:'MAD ':'symbol':'1.2-2' }}</strong></td>
          </tr>
        </tbody>
      </nz-table>

      <div class="totals-section">
        <div class="totals-row">
          <span>Sous-total:</span>
          <span>{{ invoice.subtotal | currency:'MAD ':'symbol':'1.2-2' }}</span>
        </div>
        <div class="totals-row">
          <span>TVA ({{ invoice.taxRate }}%):</span>
          <span>{{ invoice.taxAmount | currency:'MAD ':'symbol':'1.2-2' }}</span>
        </div>
        <div class="totals-row total-final">
          <span><strong>Total:</strong></span>
          <span><strong>{{ invoice.total | currency:'MAD ':'symbol':'1.2-2' }}</strong></span>
        </div>
      </div>
    </nz-card>

    <!-- Historique -->
    <nz-card class="history-card" nzTitle="Historique">
      <nz-timeline>
        <nz-timeline-item nzColor="blue">
          <p>Facture créée</p>
          <span class="timeline-date">{{ getFormattedDateTime(invoice.createdAt) }}</span>
        </nz-timeline-item>
        <nz-timeline-item nzColor="orange" *ngIf="invoice.status !== 'draft'">
          <p>Facture envoyée au client</p>
          <span class="timeline-date">{{ getFormattedDateTime(invoice.updatedAt) }}</span>
        </nz-timeline-item>
        <nz-timeline-item nzColor="green" *ngIf="invoice.status === 'paid' && invoice.paidDate">
          <p>Paiement reçu</p>
          <span class="timeline-date">{{ getFormattedDateTime(invoice.paidDate) }}</span>
        </nz-timeline-item>
        <nz-timeline-item nzColor="red" *ngIf="invoice.status === 'overdue'">
          <p>Facture en retard</p>
          <span class="timeline-date">Échue depuis le {{ getFormattedDate(invoice.dueDate) }}</span>
        </nz-timeline-item>
      </nz-timeline>
    </nz-card>
  </div>

  <!-- État de chargement -->
  <div class="loading-state" *ngIf="loading">
    <nz-spin nzSize="large">
      <div class="loading-content">Chargement des détails de la facture...</div>
    </nz-spin>
  </div>

  <!-- État d'erreur -->
  <div class="error-state" *ngIf="error">
    <nz-result
      nzStatus="error"
      nzTitle="Erreur de chargement"
      [nzSubTitle]="error"
    >
      <div nz-result-extra>
        <button nz-button nzType="primary" (click)="goBack()">Retour</button>
      </div>
    </nz-result>
  </div>
</div>
