import { Routes } from '@angular/router';
import { SpacesComponent } from './spaces.component';

export const SPACES_ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./spaces.component').then(m => m.SpacesComponent)
  },
  {
    path: 'new',
    loadComponent: () => import('../space-form/space-form.component').then(m => m.SpaceFormComponent)
  },
  {
    path: 'edit/:id',
    loadComponent: () => import('../space-form/space-form.component').then(m => m.SpaceFormComponent)
  },
  {
    path: 'details/:id',
    loadComponent: () => import('../space-details/space-details.component').then(m => m.SpaceDetailsComponent)
  },
  {
    path: 'calendar',
    loadComponent: () => import('../space-calendar/space-calendar.component').then(m => m.SpaceCalendarComponent)
  }
];
