import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

// Ng-Zorro imports
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker';
import { NzMessageService } from 'ng-zorro-antd/message';

import { SubscriptionService } from '../../services/subscription.service';
import {
  SubscriptionPlan,
  CreateSubscriptionPlanRequest,
  UpdateSubscriptionPlanRequest,
  SubscriptionType,
  MembershipType
} from '../../models/subscription.model';

@Component({
  selector: 'app-subscription-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzButtonModule,
    NzIconModule,
    NzInputModule,
    NzSelectModule,
    NzFormModule,
    NzCardModule,
    NzSpinModule,
    NzInputNumberModule,
    NzCheckboxModule,
    NzTimePickerModule
  ],
  templateUrl: './subscription-form.component.html',
  styleUrl: './subscription-form.component.css'
})
export class SubscriptionFormComponent implements OnInit {

  // Signaux pour la réactivité
  private loadingSignal = signal<boolean>(false);
  private planSignal = signal<SubscriptionPlan | null>(null);

  // Getters pour les templates
  get loading() { return this.loadingSignal(); }
  get plan() { return this.planSignal(); }

  // Propriétés du composant
  isEditMode = false;
  planId: string | null = null;
  planForm!: FormGroup;

  // Options pour les selects
  subscriptionTypeOptions = [
    { label: 'Journalier', value: SubscriptionType.DAILY },
    { label: 'Hebdomadaire', value: SubscriptionType.WEEKLY },
    { label: 'Mensuel', value: SubscriptionType.MONTHLY },
    { label: 'Flexible', value: SubscriptionType.FLEXIBLE }
  ];

  membershipTypeOptions = [
    { label: 'Étudiant', value: MembershipType.STUDENT },
    { label: 'Professionnel', value: MembershipType.PROFESSIONAL },
    { label: 'Premium', value: MembershipType.PREMIUM }
  ];

  dayOptions = [
    { label: 'Lundi', value: 'monday' },
    { label: 'Mardi', value: 'tuesday' },
    { label: 'Mercredi', value: 'wednesday' },
    { label: 'Jeudi', value: 'thursday' },
    { label: 'Vendredi', value: 'friday' },
    { label: 'Samedi', value: 'saturday' },
    { label: 'Dimanche', value: 'sunday' }
  ];

  // Enums pour les templates
  SubscriptionType = SubscriptionType;
  MembershipType = MembershipType;

  constructor(
    private fb: FormBuilder,
    private subscriptionService: SubscriptionService,
    private router: Router,
    private route: ActivatedRoute,
    private message: NzMessageService
  ) {
    this.initializeForm();
  }

  ngOnInit() {
    // Vérifier si on est en mode édition
    this.planId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.planId;

    if (this.isEditMode && this.planId) {
      this.loadPlan(this.planId);
    } else {
      // Ajouter des valeurs par défaut pour la création
      this.addTimeSlot();
      this.addFeature();
    }
  }

  private initializeForm() {
    this.planForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', [Validators.required]],
      type: [null, [Validators.required]],
      membershipTypes: [[], [Validators.required]],
      price: [0, [Validators.required, Validators.min(0)]],
      duration: [1, [Validators.min(1)]], // Validation conditionnelle ajoutée plus tard
      isActive: [true],
      features: this.fb.array([]),
      rights: this.fb.group({
        maxReservationsPerDay: [1, [Validators.required, Validators.min(1)]],
        maxReservationsPerWeek: [5, [Validators.required, Validators.min(1)]],
        maxReservationsPerMonth: [20, [Validators.required, Validators.min(1)]],
        allowedTimeSlots: this.fb.array([]),
        allowedDays: [[], [Validators.required]],
        includedRooms: [[], [Validators.required]],
        canBookMeetingRooms: [false],
        canAccessPremiumAreas: [false],
        maxConsecutiveHours: [4, [Validators.required, Validators.min(1)]],
        advanceBookingDays: [3, [Validators.required, Validators.min(1)]]
      })
    });

    // Écouter les changements du type pour ajuster la validation de durée
    this.planForm.get('type')?.valueChanges.subscribe(type => {
      const durationControl = this.planForm.get('duration');
      if (type === SubscriptionType.FLEXIBLE) {
        durationControl?.setValidators([Validators.required, Validators.min(1)]);
      } else {
        durationControl?.setValidators([Validators.min(1)]);
        // Définir des valeurs par défaut selon le type
        switch (type) {
          case SubscriptionType.DAILY:
            durationControl?.setValue(1);
            break;
          case SubscriptionType.WEEKLY:
            durationControl?.setValue(7);
            break;
          case SubscriptionType.MONTHLY:
            durationControl?.setValue(30);
            break;
        }
      }
      durationControl?.updateValueAndValidity();
    });
  }

  private loadPlan(id: string) {
    this.loadingSignal.set(true);
    this.subscriptionService.getPlanById(id).subscribe({
      next: (plan) => {
        if (plan) {
          this.planSignal.set(plan);
          this.populateForm(plan);
        } else {
          this.message.error('Plan non trouvé');
          this.router.navigate(['/subscriptions']);
        }
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement du plan:', error);
        this.message.error('Erreur lors du chargement du plan');
        this.loadingSignal.set(false);
        this.router.navigate(['/subscriptions']);
      }
    });
  }

  private populateForm(plan: SubscriptionPlan) {
    // Réinitialiser les FormArrays
    const timeSlots = this.planForm.get('rights.allowedTimeSlots') as FormArray;
    const features = this.planForm.get('features') as FormArray;

    timeSlots.clear();
    features.clear();

    // Remplir les créneaux horaires
    plan.rights.allowedTimeSlots.forEach(slot => {
      const timeSlotGroup = this.fb.group({
        start: [this.createTimeFromString(slot.start), Validators.required],
        end: [this.createTimeFromString(slot.end), Validators.required]
      });
      timeSlots.push(timeSlotGroup);
    });

    // Remplir les fonctionnalités
    plan.features.forEach(feature => {
      features.push(this.fb.control(feature, Validators.required));
    });

    // Remplir le reste du formulaire
    this.planForm.patchValue({
      name: plan.name,
      description: plan.description,
      type: plan.type,
      membershipTypes: plan.membershipTypes,
      price: plan.price,
      duration: plan.duration,
      isActive: plan.isActive,
      rights: {
        maxReservationsPerDay: plan.rights.maxReservationsPerDay,
        maxReservationsPerWeek: plan.rights.maxReservationsPerWeek,
        maxReservationsPerMonth: plan.rights.maxReservationsPerMonth,
        allowedDays: plan.rights.allowedDays,
        includedRooms: plan.rights.includedRooms,
        canBookMeetingRooms: plan.rights.canBookMeetingRooms,
        canAccessPremiumAreas: plan.rights.canAccessPremiumAreas,
        maxConsecutiveHours: plan.rights.maxConsecutiveHours,
        advanceBookingDays: plan.rights.advanceBookingDays
      }
    });
  }

  // Gestion des créneaux horaires
  get timeSlots() {
    return this.planForm.get('rights.allowedTimeSlots') as FormArray;
  }

  addTimeSlot() {
    const timeSlotGroup = this.fb.group({
      start: [this.createTimeFromString('08:00'), Validators.required],
      end: [this.createTimeFromString('18:00'), Validators.required]
    });
    this.timeSlots.push(timeSlotGroup);
  }

  removeTimeSlot(index: number) {
    if (this.timeSlots.length > 1) {
      this.timeSlots.removeAt(index);
    }
  }

  // Gestion des fonctionnalités
  get features() {
    return this.planForm.get('features') as FormArray;
  }

  addFeature() {
    this.features.push(this.fb.control('', Validators.required));
  }

  removeFeature(index: number) {
    if (this.features.length > 1) {
      this.features.removeAt(index);
    }
  }

  onSubmit() {
    if (this.planForm.valid) {
      this.loadingSignal.set(true);

      if (this.isEditMode && this.planId) {
        this.updatePlan();
      } else {
        this.createPlan();
      }
    } else {
      this.message.warning('Veuillez remplir tous les champs requis');
      this.markFormGroupTouched();
    }
  }

  private createPlan() {
    const formValue = this.planForm.value;
    const request: CreateSubscriptionPlanRequest = {
      name: formValue.name,
      description: formValue.description,
      type: formValue.type,
      membershipTypes: formValue.membershipTypes,
      price: formValue.price,
      duration: formValue.duration,
      features: formValue.features.filter((f: string) => f.trim()),
      rights: {
        ...formValue.rights,
        allowedTimeSlots: formValue.rights.allowedTimeSlots.map((slot: any) => ({
          start: this.formatTimeToString(slot.start),
          end: this.formatTimeToString(slot.end)
        }))
      }
    };

    this.subscriptionService.createPlan(request).subscribe({
      next: (plan) => {
        this.message.success('Plan créé avec succès');
        this.router.navigate(['/subscriptions']);
      },
      error: (error) => {
        console.error('Erreur lors de la création:', error);
        this.message.error('Erreur lors de la création du plan');
        this.loadingSignal.set(false);
      }
    });
  }

  private updatePlan() {
    const formValue = this.planForm.value;
    const request: UpdateSubscriptionPlanRequest = {
      name: formValue.name,
      description: formValue.description,
      type: formValue.type,
      membershipTypes: formValue.membershipTypes,
      price: formValue.price,
      duration: formValue.duration,
      features: formValue.features.filter((f: string) => f.trim()),
      isActive: formValue.isActive,
      rights: {
        ...formValue.rights,
        allowedTimeSlots: formValue.rights.allowedTimeSlots.map((slot: any) => ({
          start: this.formatTimeToString(slot.start),
          end: this.formatTimeToString(slot.end)
        }))
      }
    };

    this.subscriptionService.updatePlan(this.planId!, request).subscribe({
      next: (plan) => {
        this.message.success('Plan modifié avec succès');
        this.router.navigate(['/subscriptions']);
      },
      error: (error) => {
        console.error('Erreur lors de la modification:', error);
        this.message.error('Erreur lors de la modification du plan');
        this.loadingSignal.set(false);
      }
    });
  }

  onCancel() {
    this.router.navigate(['/subscriptions']);
  }

  private markFormGroupTouched() {
    Object.keys(this.planForm.controls).forEach(key => {
      const control = this.planForm.get(key);
      control?.markAsTouched();
    });
  }

  // Méthodes pour les salles (mock)
  getRoomOptions() {
    return [
      { label: 'Salle 1', value: 'room1' },
      { label: 'Salle 2', value: 'room2' },
      { label: 'Salle 3', value: 'room3' },
      { label: 'Salle 4', value: 'room4' },
      { label: 'Salle 5', value: 'room5' },
      { label: 'Salle de réunion 1', value: 'meeting1' },
      { label: 'Salle de réunion 2', value: 'meeting2' }
    ];
  }

  getPageTitle(): string {
    return this.isEditMode ? 'Modifier le plan' : 'Nouveau plan d\'abonnement';
  }

  getPageIcon(): string {
    return this.isEditMode ? 'edit' : 'credit-card';
  }

  // Méthodes utilitaires pour les time-pickers
  private createTimeFromString(timeString: string): Date {
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    return date;
  }

  private formatTimeToString(date: Date): string {
    if (!date) return '';
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }
}
