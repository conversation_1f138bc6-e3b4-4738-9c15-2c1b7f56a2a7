import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Invoice, BillingStats, PaymentHistory, InvoiceItem } from '../models/invoice.model';
import { MemberService } from './member.service';

@Injectable({
  providedIn: 'root'
})
export class BillingService {

  constructor(private memberService: MemberService) { }

  // Récupérer toutes les factures
  getInvoices(): Observable<Invoice[]> {
    return of(this.getMockInvoices());
  }

  // Récupérer une facture par ID
  getInvoice(id: string): Observable<Invoice> {
    const invoices = this.getMockInvoices();
    const invoice = invoices.find(i => i.id === id);
    if (!invoice) {
      throw new Error('Facture non trouvée');
    }
    return of(invoice);
  }

  // Mettre à jour une facture
  updateInvoice(id: string, invoiceData: any): Observable<Invoice> {
    const invoices = this.getMockInvoices();
    const index = invoices.findIndex(i => i.id === id);

    if (index !== -1) {
      const updatedInvoice = {
        ...invoices[index],
        ...invoiceData,
        updatedAt: new Date()
      };
      invoices[index] = updatedInvoice;
      return of(updatedInvoice);
    }

    throw new Error('Facture non trouvée');
  }

  // Récupérer les statistiques de facturation
  getBillingStats(): Observable<BillingStats> {
    const invoices = this.getMockInvoices();

    const stats: BillingStats = {
      totalRevenue: invoices.filter(i => i.status === 'paid').reduce((sum, i) => sum + i.total, 0),
      monthlyRevenue: this.getMonthlyRevenue(invoices),
      pendingAmount: invoices.filter(i => i.status === 'sent').reduce((sum, i) => sum + i.total, 0),
      overdueAmount: invoices.filter(i => i.status === 'overdue').reduce((sum, i) => sum + i.total, 0),
      totalInvoices: invoices.length,
      paidInvoices: invoices.filter(i => i.status === 'paid').length,
      pendingInvoices: invoices.filter(i => i.status === 'sent').length,
      overdueInvoices: invoices.filter(i => i.status === 'overdue').length
    };

    return of(stats);
  }

  // Générer une nouvelle facture
  generateInvoice(memberId: string, items: InvoiceItem[]): Observable<Invoice> {
    const invoice = this.createInvoice(memberId, items);
    return of(invoice);
  }

  // Marquer une facture comme payée
  markAsPaid(invoiceId: string, paymentMethod: string): Observable<Invoice> {
    const invoices = this.getMockInvoices();
    const invoice = invoices.find(i => i.id === invoiceId);

    if (invoice) {
      invoice.status = 'paid';
      invoice.paidDate = new Date();
      invoice.paymentMethod = paymentMethod as any;
      invoice.updatedAt = new Date();
    }

    return of(invoice!);
  }

  // Exporter les factures en CSV
  exportToCSV(invoices: Invoice[]): string {
    const headers = [
      'Numéro',
      'Client',
      'Email',
      'Date émission',
      'Date échéance',
      'Statut',
      'Montant HT',
      'TVA',
      'Montant TTC',
      'Méthode paiement'
    ];

    const csvContent = [
      headers.join(','),
      ...invoices.map(invoice => [
        invoice.invoiceNumber,
        invoice.memberName,
        invoice.memberEmail,
        invoice.issueDate.toLocaleDateString('fr-FR'),
        invoice.dueDate.toLocaleDateString('fr-FR'),
        this.getStatusLabel(invoice.status),
        invoice.subtotal.toFixed(2),
        invoice.taxAmount.toFixed(2),
        invoice.total.toFixed(2),
        invoice.paymentMethod || 'N/A'
      ].join(','))
    ].join('\n');

    return csvContent;
  }

  // Générer automatiquement les factures mensuelles
  generateMonthlyInvoices(): Observable<Invoice[]> {
    // Simulation de génération automatique
    const members = this.getMockMembers();
    const invoices: Invoice[] = [];

    members.forEach(member => {
      if (member.subscriptionType && member.subscriptionType !== 'None') {
        const items: InvoiceItem[] = [{
          id: this.generateId(),
          description: `Abonnement ${member.subscriptionType} - ${this.getCurrentMonthLabel()}`,
          quantity: 1,
          unitPrice: this.getSubscriptionPrice(member.subscriptionType),
          total: this.getSubscriptionPrice(member.subscriptionType),
          type: 'subscription',
          period: {
            start: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            end: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0)
          }
        }];

        const invoice = this.createInvoice(member.id, items);
        invoices.push(invoice);
      }
    });

    return of(invoices);
  }

  private getMockInvoices(): Invoice[] {
    return [
      {
        id: '1',
        invoiceNumber: 'FACT-2024-001',
        memberId: '1',
        memberName: 'Houssam Benali',
        memberEmail: '<EMAIL>',
        memberCompany: 'TechnoMaroc',
        issueDate: new Date(2024, 0, 1),
        dueDate: new Date(2024, 0, 31),
        paidDate: new Date(2024, 0, 15),
        status: 'paid',
        subtotal: 1800,
        taxRate: 20,
        taxAmount: 360,
        total: 2160,
        items: [{
          id: '1',
          description: 'Abonnement Professionnel Premium - Janvier 2024',
          quantity: 1,
          unitPrice: 1800,
          total: 1800,
          type: 'subscription',
          period: {
            start: new Date(2024, 0, 1),
            end: new Date(2024, 0, 31)
          }
        }],
        paymentMethod: 'card',
        createdAt: new Date(2024, 0, 1),
        updatedAt: new Date(2024, 0, 15)
      },
      {
        id: '2',
        invoiceNumber: 'FACT-2024-002',
        memberId: '2',
        memberName: 'Aicha El Fassi',
        memberEmail: '<EMAIL>',
        memberCompany: 'Design Créatif',
        issueDate: new Date(2024, 0, 1),
        dueDate: new Date(2024, 0, 31),
        status: 'sent',
        subtotal: 450,
        taxRate: 20,
        taxAmount: 90,
        total: 540,
        items: [{
          id: '2',
          description: 'Abonnement Étudiant Premium - Janvier 2024',
          quantity: 1,
          unitPrice: 450,
          total: 450,
          type: 'subscription'
        }],
        createdAt: new Date(2024, 0, 1),
        updatedAt: new Date(2024, 0, 1)
      },
      {
        id: '3',
        invoiceNumber: 'FACT-2024-003',
        memberId: '3',
        memberName: 'Omar Tazi',
        memberEmail: '<EMAIL>',
        memberCompany: 'InnovMaroc',
        issueDate: new Date(2023, 11, 1),
        dueDate: new Date(2023, 11, 31),
        status: 'overdue',
        subtotal: 1800,
        taxRate: 20,
        taxAmount: 360,
        total: 2160,
        items: [{
          id: '3',
          description: 'Abonnement Professionnel Premium - Décembre 2023',
          quantity: 1,
          unitPrice: 1800,
          total: 1800,
          type: 'subscription'
        }],
        createdAt: new Date(2023, 11, 1),
        updatedAt: new Date(2023, 11, 1)
      },
      {
        id: '4',
        invoiceNumber: 'FACT-2024-004',
        memberId: '4',
        memberName: 'Fatima Alaoui',
        memberEmail: '<EMAIL>',
        memberCompany: 'Alaoui Consulting',
        issueDate: new Date(2024, 0, 15),
        dueDate: new Date(2024, 1, 15),
        paidDate: new Date(2024, 1, 10),
        status: 'paid',
        subtotal: 900,
        taxRate: 20,
        taxAmount: 180,
        total: 1080,
        items: [{
          id: '4',
          description: 'Abonnement Professionnel Standard - Février 2024',
          quantity: 1,
          unitPrice: 900,
          total: 900,
          type: 'subscription'
        }],
        paymentMethod: 'transfer',
        createdAt: new Date(2024, 0, 15),
        updatedAt: new Date(2024, 1, 10)
      },
      {
        id: '5',
        invoiceNumber: 'FACT-2024-005',
        memberId: '5',
        memberName: 'Mehdi Benjelloun',
        memberEmail: '<EMAIL>',
        memberCompany: 'Freelance',
        issueDate: new Date(2024, 1, 1),
        dueDate: new Date(2024, 1, 28),
        status: 'sent',
        subtotal: 450,
        taxRate: 20,
        taxAmount: 90,
        total: 540,
        items: [{
          id: '5',
          description: 'Abonnement Étudiant Premium - Février 2024',
          quantity: 1,
          unitPrice: 450,
          total: 450,
          type: 'subscription'
        }],
        createdAt: new Date(2024, 1, 1),
        updatedAt: new Date(2024, 1, 1)
      },
      {
        id: '6',
        invoiceNumber: 'FACT-2024-006',
        memberId: '6',
        memberName: 'Salma Chraibi',
        memberEmail: '<EMAIL>',
        memberCompany: 'Université Mohammed V',
        issueDate: new Date(2024, 1, 15),
        dueDate: new Date(2024, 2, 15),
        paidDate: new Date(2024, 2, 5),
        status: 'paid',
        subtotal: 270,
        taxRate: 20,
        taxAmount: 54,
        total: 324,
        items: [{
          id: '6',
          description: 'Abonnement Étudiant Basic - Mars 2024',
          quantity: 1,
          unitPrice: 270,
          total: 270,
          type: 'subscription'
        }],
        paymentMethod: 'card',
        createdAt: new Date(2024, 1, 15),
        updatedAt: new Date(2024, 2, 5)
      },
      {
        id: '7',
        invoiceNumber: 'FACT-2024-007',
        memberId: '7',
        memberName: 'Karim Ouali',
        memberEmail: '<EMAIL>',
        memberCompany: 'Ouali Architecture',
        issueDate: new Date(2024, 1, 20),
        dueDate: new Date(2024, 2, 20),
        status: 'sent',
        subtotal: 900,
        taxRate: 20,
        taxAmount: 180,
        total: 1080,
        items: [{
          id: '7',
          description: 'Abonnement Professionnel Standard - Mars 2024',
          quantity: 1,
          unitPrice: 900,
          total: 900,
          type: 'subscription'
        }],
        createdAt: new Date(2024, 1, 20),
        updatedAt: new Date(2024, 1, 20)
      },
      {
        id: '8',
        invoiceNumber: 'FACT-2024-008',
        memberId: '8',
        memberName: 'Nadia Bennani',
        memberEmail: '<EMAIL>',
        memberCompany: 'Cabinet Bennani',
        issueDate: new Date(2024, 2, 1),
        dueDate: new Date(2024, 2, 31),
        paidDate: new Date(2024, 2, 12),
        status: 'paid',
        subtotal: 1800,
        taxRate: 20,
        taxAmount: 360,
        total: 2160,
        items: [{
          id: '8',
          description: 'Abonnement Professionnel Premium - Mars 2024',
          quantity: 1,
          unitPrice: 1800,
          total: 1800,
          type: 'subscription'
        }],
        paymentMethod: 'transfer',
        createdAt: new Date(2024, 2, 1),
        updatedAt: new Date(2024, 2, 12)
      }
    ];
  }

  // Récupérer l'historique des paiements pour un membre
  getPaymentHistory(memberId: string): Observable<PaymentHistory[]> {
    const mockPayments: PaymentHistory[] = [
      {
        id: '1',
        invoiceId: '1',
        amount: 2160,
        method: 'card',
        date: new Date(2024, 0, 15),
        reference: 'PAY-2024-001',
        notes: 'Paiement automatique par carte Visa'
      },
      {
        id: '2',
        invoiceId: '4',
        amount: 1080,
        method: 'transfer',
        date: new Date(2024, 1, 10),
        reference: 'PAY-2024-002',
        notes: 'Virement bancaire - Attijariwafa Bank'
      },
      {
        id: '3',
        invoiceId: '6',
        amount: 324,
        method: 'card',
        date: new Date(2024, 2, 5),
        reference: 'PAY-2024-003',
        notes: 'Paiement par carte étudiante'
      },
      {
        id: '4',
        invoiceId: '8',
        amount: 2160,
        method: 'transfer',
        date: new Date(2024, 2, 12),
        reference: 'PAY-2024-004',
        notes: 'Virement bancaire - BMCE Bank'
      },
      {
        id: '5',
        invoiceId: '1',
        amount: 2160,
        method: 'card',
        date: new Date(2023, 11, 20),
        reference: 'PAY-2023-045',
        notes: 'Paiement abonnement décembre'
      },
      {
        id: '6',
        invoiceId: '2',
        amount: 540,
        method: 'card',
        date: new Date(2023, 10, 15),
        reference: 'PAY-2023-044',
        notes: 'Paiement par carte Mastercard'
      }
    ];

    return of(mockPayments.filter(p => {
      const invoice = this.getMockInvoices().find(i => i.id === p.invoiceId);
      return invoice?.memberId === memberId;
    }));
  }

  private getMockMembers() {
    return [
      {
        id: '1',
        firstName: 'Jean',
        lastName: 'Dupont',
        email: '<EMAIL>',
        subscriptionType: 'Premium'
      },
      {
        id: '2',
        firstName: 'Marie',
        lastName: 'Martin',
        email: '<EMAIL>',
        subscriptionType: 'Standard'
      },
      {
        id: '3',
        firstName: 'Pierre',
        lastName: 'Durand',
        email: '<EMAIL>',
        subscriptionType: 'Basic'
      }
    ];
  }

  private createInvoice(memberId: string, items: InvoiceItem[]): Invoice {
    const member = this.getMockMembers().find(m => m.id === memberId);
    const subtotal = items.reduce((sum, item) => sum + item.total, 0);
    const taxRate = 20;
    const taxAmount = subtotal * (taxRate / 100);
    const total = subtotal + taxAmount;

    return {
      id: this.generateId(),
      invoiceNumber: this.generateInvoiceNumber(),
      memberId: memberId,
      memberName: member ? `${member.firstName} ${member.lastName}` : 'Client inconnu',
      memberEmail: member?.email || '',
      issueDate: new Date(),
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 jours
      status: 'draft',
      subtotal: subtotal,
      taxRate: taxRate,
      taxAmount: taxAmount,
      total: total,
      items: items,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  private getMonthlyRevenue(invoices: Invoice[]): number {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    return invoices
      .filter(i =>
        i.status === 'paid' &&
        i.paidDate &&
        i.paidDate.getMonth() === currentMonth &&
        i.paidDate.getFullYear() === currentYear
      )
      .reduce((sum, i) => sum + i.total, 0);
  }

  private getSubscriptionPrice(subscriptionType: string): number {
    switch (subscriptionType) {
      case 'Étudiant Basic': return 270;
      case 'Étudiant Premium': return 450;
      case 'Professionnel Standard': return 900;
      case 'Professionnel Premium': return 1800;
      default: return 0;
    }
  }

  private getCurrentMonthLabel(): string {
    const months = [
      'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
      'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];
    return `${months[new Date().getMonth()]} ${new Date().getFullYear()}`;
  }

  private getStatusLabel(status: string): string {
    switch (status) {
      case 'draft': return 'Brouillon';
      case 'sent': return 'Envoyée';
      case 'paid': return 'Payée';
      case 'overdue': return 'En retard';
      case 'cancelled': return 'Annulée';
      default: return status;
    }
  }

  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  private generateInvoiceNumber(): string {
    const year = new Date().getFullYear();
    const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `INV-${year}-${month}-${random}`;
  }
}
