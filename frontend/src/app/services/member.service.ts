import { Injectable, signal } from '@angular/core';
import { Observable, of, delay, map } from 'rxjs';
import {
  Member,
  MemberType,
  MemberStatus,
  MemberHistory,
  HistoryType,
  Subscription,
  CreateMemberRequest,
  UpdateMemberRequest
} from '../models/member.model';

@Injectable({
  providedIn: 'root'
})
export class MemberService {
  // Signaux pour la gestion d'état
  private membersSignal = signal<Member[]>([]);
  private subscriptionsSignal = signal<Subscription[]>([]);
  private loadingSignal = signal<boolean>(false);

  // Getters publics pour les signaux
  members = this.membersSignal.asReadonly();
  subscriptions = this.subscriptionsSignal.asReadonly();
  loading = this.loadingSignal.asReadonly();

  constructor() {
    this.initializeMockData();
  }

  // Initialisation avec des données de test
  private initializeMockData() {
    const mockSubscriptions: Subscription[] = [
      {
        id: '1',
        name: 'Étudiant Basic',
        description: 'Accès de base pour étudiants',
        price: 299,
        duration: 30,
        features: ['Accès aux espaces communs', 'WiFi gratuit', '4h/jour'],
        isActive: true,
        memberType: MemberType.STUDENT
      },
      {
        id: '2',
        name: 'Étudiant Premium',
        description: 'Accès complet pour étudiants',
        price: 499,
        duration: 30,
        features: ['Accès illimité', 'Salles de réunion', 'Casier personnel'],
        isActive: true,
        memberType: MemberType.STUDENT
      },
      {
        id: '3',
        name: 'Professionnel Standard',
        description: 'Plan standard pour professionnels',
        price: 999,
        duration: 30,
        features: ['Accès illimité', 'Bureau dédié', 'Adresse postale'],
        isActive: true,
        memberType: MemberType.PROFESSIONAL
      },
      {
        id: '4',
        name: 'Professionnel Premium',
        description: 'Plan premium pour professionnels',
        price: 1999,
        duration: 30,
        features: ['Bureau privé', 'Salle de réunion incluse', 'Services premium'],
        isActive: true,
        memberType: MemberType.PROFESSIONAL
      }
    ];

    const mockMembers: Member[] = [
      {
        id: '1',
        firstName: 'Aicha',
        lastName: 'Benali',
        email: '<EMAIL>',
        phone: '+212 6 12 34 56 78',
        memberType: MemberType.STUDENT,
        subscriptionType: 'Étudiant Premium',
        status: MemberStatus.ACTIVE,
        studentCode: 'ETU2024001',
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: '2',
        firstName: 'Omar',
        lastName: 'El Fassi',
        email: '<EMAIL>',
        phone: '+212 6 98 76 54 32',
        memberType: MemberType.PROFESSIONAL,
        subscriptionType: 'Professionnel Standard',
        status: MemberStatus.ACTIVE,
        createdAt: new Date('2024-02-01'),
        updatedAt: new Date('2024-02-01')
      },
      {
        id: '3',
        firstName: 'Fatima',
        lastName: 'Zahra',
        email: '<EMAIL>',
        phone: '+212 6 11 22 33 44',
        memberType: MemberType.STUDENT,
        subscriptionType: 'Étudiant Basic',
        status: MemberStatus.INACTIVE,
        studentCode: 'ETU2024002',
        createdAt: new Date('2024-01-20'),
        updatedAt: new Date('2024-01-25')
      },
      {
        id: '4',
        firstName: 'Houssam',
        lastName: 'Alaoui',
        email: '<EMAIL>',
        phone: '+212 6 55 44 33 22',
        memberType: MemberType.PROFESSIONAL,
        subscriptionType: 'Professionnel Premium',
        status: MemberStatus.ACTIVE,
        createdAt: new Date('2024-02-10'),
        updatedAt: new Date('2024-02-10')
      },
      {
        id: '5',
        firstName: 'Khadija',
        lastName: 'Benjelloun',
        email: '<EMAIL>',
        phone: '+212 6 77 88 99 00',
        memberType: MemberType.STUDENT,
        subscriptionType: 'Étudiant Premium',
        status: MemberStatus.ACTIVE,
        studentCode: 'ETU2024003',
        createdAt: new Date('2024-02-15'),
        updatedAt: new Date('2024-02-15')
      },
      {
        id: '6',
        firstName: 'Hassan',
        lastName: 'Tazi',
        email: '<EMAIL>',
        phone: '+212 6 33 22 11 00',
        memberType: MemberType.PROFESSIONAL,
        subscriptionType: 'Professionnel Standard',
        status: MemberStatus.SUSPENDED,
        createdAt: new Date('2024-01-30'),
        updatedAt: new Date('2024-02-20')
      }
    ];

    this.subscriptionsSignal.set(mockSubscriptions);
    this.membersSignal.set(mockMembers);
  }

  // Récupérer tous les membres
  getMembers(): Observable<Member[]> {
    this.loadingSignal.set(true);
    return of(this.membersSignal()).pipe(
      delay(500), // Simulation d'un appel API
      map(members => {
        this.loadingSignal.set(false);
        return members;
      })
    );
  }

  // Récupérer un membre par ID
  getMemberById(id: string): Observable<Member | undefined> {
    return of(this.membersSignal().find(member => member.id === id)).pipe(delay(300));
  }

  // Créer un nouveau membre
  createMember(memberData: CreateMemberRequest): Observable<Member> {
    this.loadingSignal.set(true);

    const newMember: Member = {
      id: Date.now().toString(),
      ...memberData,
      subscriptionType: this.subscriptionsSignal().find(s => s.id === memberData.subscriptionId)?.name || '',
      status: MemberStatus.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return of(newMember).pipe(
      delay(800),
      map(member => {
        const currentMembers = this.membersSignal();
        this.membersSignal.set([...currentMembers, member]);
        this.loadingSignal.set(false);
        return member;
      })
    );
  }

  // Mettre à jour un membre
  updateMember(id: string, updateData: UpdateMemberRequest): Observable<Member> {
    this.loadingSignal.set(true);

    return of(updateData).pipe(
      delay(600),
      map(data => {
        const currentMembers = this.membersSignal();
        const memberIndex = currentMembers.findIndex(m => m.id === id);

        if (memberIndex !== -1) {
          const updatedMember = {
            ...currentMembers[memberIndex],
            ...data,
            updatedAt: new Date()
          };

          if (data.subscriptionId) {
            updatedMember.subscriptionType = this.subscriptionsSignal()
              .find(s => s.id === data.subscriptionId)?.name || updatedMember.subscriptionType;
          }

          const newMembers = [...currentMembers];
          newMembers[memberIndex] = updatedMember;
          this.membersSignal.set(newMembers);
          this.loadingSignal.set(false);
          return updatedMember;
        }

        this.loadingSignal.set(false);
        throw new Error('Membre non trouvé');
      })
    );
  }

  // Supprimer un membre
  deleteMember(id: string): Observable<boolean> {
    this.loadingSignal.set(true);

    return of(true).pipe(
      delay(500),
      map(() => {
        const currentMembers = this.membersSignal();
        const filteredMembers = currentMembers.filter(m => m.id !== id);
        this.membersSignal.set(filteredMembers);
        this.loadingSignal.set(false);
        return true;
      })
    );
  }

  // Récupérer les abonnements
  getSubscriptions(): Observable<Subscription[]> {
    return of(this.subscriptionsSignal()).pipe(delay(200));
  }

  // Récupérer les abonnements par type de membre
  getSubscriptionsByType(memberType: MemberType): Observable<Subscription[]> {
    return of(this.subscriptionsSignal().filter(s =>
      !s.memberType || s.memberType === memberType
    )).pipe(delay(200));
  }

  // Récupérer l'historique d'un membre
  getMemberHistory(memberId: string): Observable<MemberHistory[]> {
    // Mock data pour l'historique
    const mockHistory: MemberHistory[] = [
      {
        id: '1',
        memberId,
        type: HistoryType.PAYMENT,
        description: 'Paiement abonnement mensuel',
        amount: 499,
        date: new Date('2024-07-01'),
        details: { method: 'carte', reference: 'PAY-2024-001' }
      },
      {
        id: '2',
        memberId,
        type: HistoryType.RESERVATION,
        description: 'Réservation salle de réunion A',
        date: new Date('2024-07-15'),
        details: { room: 'Salle A', duration: '2h', time: '14:00-16:00' }
      }
    ];

    return of(mockHistory).pipe(delay(300));
  }
}
