import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import Keycloak from 'keycloak-js';
import { keycloakConfig, keycloakInitOptions } from '../config/keycloak.config';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private keycloak: Keycloak | undefined;

  constructor(private router: Router) {}

  async init(): Promise<boolean> {
    try {
      this.keycloak = new Keycloak(keycloakConfig);
      const authenticated = await this.keycloak.init(keycloakInitOptions);

      if (authenticated) {
        console.log('User authenticated successfully');
        console.log('Token:', this.keycloak.token);

        // Vérifier s'il y a une URL de redirection stockée
        this.handlePostLoginRedirect();
      }

      return authenticated;
    } catch (error) {
      console.error('Keycloak initialization failed:', error);
      return false;
    }
  }

  getKeycloak(): Keycloak | undefined {
    return this.keycloak;
  }

  isAuthenticated(): boolean {
    return this.keycloak?.authenticated ?? false;
  }

  getToken(): string | undefined {
    return this.keycloak?.token;
  }

  getUsername(): string | undefined {
    return this.keycloak?.tokenParsed?.['preferred_username'];
  }

  getUserRoles(): string[] {
    const realmAccess = this.keycloak?.tokenParsed?.['realm_access'];
    return realmAccess?.['roles'] || [];
  }

  hasRole(role: string): boolean {
    return this.getUserRoles().includes(role);
  }

  login(): void {
    this.keycloak?.login();
  }

  logout(): void {
    // Effacer le tenant ID stocké
    localStorage.removeItem('workeem_tenant_id');
    this.keycloak?.logout({
      redirectUri: window.location.origin
    });
  }

  refreshToken(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (this.keycloak) {
        this.keycloak.updateToken(30)
          .then(refreshed => {
            if (refreshed) {
              console.log('Token refreshed');
            }
            resolve(refreshed);
          })
          .catch(error => {
            console.error('Failed to refresh token:', error);
            reject(error);
          });
      } else {
        reject('Keycloak not initialized');
      }
    });
  }

  private handlePostLoginRedirect(): void {
    const redirectUrl = localStorage.getItem('workeem_redirect_url');
    const tenantId = localStorage.getItem('workeem_tenant_id');

    if (redirectUrl && tenantId) {
      console.log('🔄 Redirecting to stored URL after login:', redirectUrl);
      localStorage.removeItem('workeem_redirect_url');
      this.router.navigateByUrl(redirectUrl);
    }
  }
}
