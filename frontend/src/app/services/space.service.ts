import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import {
  Space,
  SpaceType,
  SpaceStatus,
  Equipment,
  EquipmentType,
  EquipmentStatus,
  CreateSpaceRequest,
  UpdateSpaceRequest,
  SpaceSearchFilters,
  SpaceReservation,
  ReservationStatus,
  CalendarEvent,
  CalendarResource
} from '../models/space.model';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SpaceService {
  private readonly apiUrl = `${environment.apiUrl}/api/spaces`;

  private spacesSubject = new BehaviorSubject<Space[]>([]);
  public spaces$ = this.spacesSubject.asObservable();

  private reservationsSubject = new BehaviorSubject<SpaceReservation[]>([]);
  public reservations$ = this.reservationsSubject.asObservable();

  constructor(private http: HttpClient) {
  }

  /**
   * Get current site ID from context (should be implemented based on your site management)
   */
  private getCurrentSiteId(): string {
    // TODO: Implement proper site context management
    // For now, return a default site ID
    return localStorage.getItem('currentSiteId') || 'default-site';
  }

  // Méthodes CRUD pour les espaces
  getSpaces(): Observable<Space[]> {
    const params = new HttpParams().set('siteId', this.getCurrentSiteId());

    return this.http.get<any[]>(`${this.apiUrl}`, { params }).pipe(
      map(response => this.mapSpacesFromBackend(response)),
      catchError(error => {
        console.error('Error fetching spaces:', error);
        return throwError(() => error);
      })
    );
  }

  getSpaceById(id: string): Observable<Space | null> {
    return this.http.get<any>(`${this.apiUrl}/${id}`).pipe(
      map(response => this.mapSpaceFromBackend(response)),
      catchError(error => {
        console.error('Error fetching space by id:', error);
        return throwError(() => error);
      })
    );
  }

  createSpace(request: CreateSpaceRequest): Observable<Space> {
    const params = new HttpParams().set('siteId', this.getCurrentSiteId());
    const body = this.mapCreateRequestToBackend(request);

    return this.http.post<any>(`${this.apiUrl}`, body, { params }).pipe(
      map(response => this.mapSpaceFromBackend(response)),
      catchError(error => {
        console.error('Error creating space:', error);
        return throwError(() => error);
      })
    );
  }

  updateSpace(request: UpdateSpaceRequest): Observable<Space> {
    const params = new HttpParams().set('siteId', this.getCurrentSiteId());
    const body = this.mapUpdateRequestToBackend(request);

    return this.http.put<any>(`${this.apiUrl}/${request.id}`, body, { params }).pipe(
      map(response => this.mapSpaceFromBackend(response)),
      catchError(error => {
        console.error('Error updating space:', error);
        return throwError(() => error);
      })
    );
  }

  deleteSpace(id: string): Observable<boolean> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`).pipe(
      map(() => true),
      catchError(error => {
        console.error('Error deleting space:', error);
        return throwError(() => error);
      })
    );
  }

  searchSpaces(filters: SpaceSearchFilters): Observable<Space[]> {
    let params = new HttpParams().set('siteId', this.getCurrentSiteId());

    if (filters.type) {
      params = params.set('type', filters.type);
    }
    if (filters.capacity) {
      params = params.set('minCapacity', filters.capacity.toString());
    }
    if (filters.location) {
      params = params.set('location', filters.location);
    }
    if (filters.floor) {
      params = params.set('floor', filters.floor);
    }

    return this.http.get<any[]>(`${this.apiUrl}`, { params }).pipe(
      map(response => this.mapSpacesFromBackend(response)),
      catchError(error => {
        console.error('Error searching spaces:', error);
        return throwError(() => error);
      })
    );
  }

  // Méthodes pour les réservations (à implémenter avec le backend des réservations)
  getReservations(): Observable<SpaceReservation[]> {
    // TODO: Implement with reservations backend
    return this.reservations$;
  }

  getReservationsBySpace(spaceId: string): Observable<SpaceReservation[]> {
    // TODO: Implement with reservations backend
    return this.reservations$.pipe(
      map(reservations => reservations.filter(res => res.spaceId === spaceId))
    );
  }

  // Méthodes pour le planning visuel
  getCalendarEvents(startDate: Date, endDate: Date): Observable<CalendarEvent[]> {
    // TODO: Implement with reservations backend
    return this.reservations$.pipe(
      map(reservations => {
        return reservations
          .filter(res => res.startTime >= startDate && res.endTime <= endDate)
          .map(res => ({
            id: res.id,
            title: `${res.spaceName} - ${res.userName}`,
            start: res.startTime,
            end: res.endTime,
            resourceId: res.spaceId,
            color: this.getStatusColor(res.status),
            status: res.status,
            attendees: res.attendees,
            userName: res.userName
          }));
      })
    );
  }

  getCalendarResources(): Observable<CalendarResource[]> {
    return this.getSpaces().pipe(
      map(spaces => {
        return spaces.map(space => ({
          id: space.id,
          title: space.name,
          type: space.type,
          capacity: space.capacity,
          location: space.location,
          status: space.status
        }));
      })
    );
  }

  // Méthodes de mapping entre frontend et backend
  private mapSpacesFromBackend(backendSpaces: any[]): Space[] {
    return backendSpaces.map(space => this.mapSpaceFromBackend(space));
  }

  private mapSpaceFromBackend(backendSpace: any): Space {
    return {
      id: backendSpace.id?.toString() || '',
      name: backendSpace.name || '',
      description: backendSpace.description || null,
      type: backendSpace.type || null,
      capacity: backendSpace.capacity || null,
      location: backendSpace.location || null,
      floor: backendSpace.floor || null,
      area: backendSpace.area || null,
      status: backendSpace.status || SpaceStatus.AVAILABLE,
      equipment: [], // TODO: Map equipment from backend
      amenities: backendSpace.amenities || [],
      images: backendSpace.images || [],
      availability: this.getDefaultAvailability(), // TODO: Map availability from backend
      pricing: {
        hourlyRate: backendSpace.hourlyRate || null,
        dailyRate: backendSpace.dailyRate || null,
        weeklyRate: backendSpace.weeklyRate || null,
        monthlyRate: backendSpace.monthlyRate || null,
        currency: 'MAD',
        discounts: []
      },
      rules: backendSpace.rules || [],
      createdAt: backendSpace.createdAt ? new Date(backendSpace.createdAt) : new Date(),
      updatedAt: backendSpace.updatedAt ? new Date(backendSpace.updatedAt) : new Date()
    };
  }

  private mapCreateRequestToBackend(request: CreateSpaceRequest): any {
    return {
      name: request.name,
      description: request.description,
      type: request.type,
      capacity: request.capacity,
      location: request.location,
      floor: request.floor,
      area: request.area,
      amenities: request.amenities,
      rules: request.rules,
      images: [],
      hourlyRate: request.pricing?.hourlyRate || null,
      dailyRate: request.pricing?.dailyRate || null,
      weeklyRate: request.pricing?.weeklyRate || null,
      monthlyRate: request.pricing?.monthlyRate || null,
      equipment: request.equipment?.map(eq => ({
        name: eq.name,
        type: eq.type,
        brand: eq.brand,
        model: eq.model,
        quantity: eq.quantity,
        status: eq.status,
        description: eq.description
      })) || [],
      availability: request.availability ? {
        isActive: request.availability.isActive,
        advanceBookingDays: request.availability.advanceBookingDays,
        minBookingDuration: request.availability.minBookingDuration,
        maxBookingDuration: request.availability.maxBookingDuration,
        bufferTime: request.availability.bufferTime,
        weeklySchedule: JSON.stringify({
          monday: request.availability.schedule?.monday || { isOpen: false },
          tuesday: request.availability.schedule?.tuesday || { isOpen: false },
          wednesday: request.availability.schedule?.wednesday || { isOpen: false },
          thursday: request.availability.schedule?.thursday || { isOpen: false },
          friday: request.availability.schedule?.friday || { isOpen: false },
          saturday: request.availability.schedule?.saturday || { isOpen: false },
          sunday: request.availability.schedule?.sunday || { isOpen: false }
        }),
        exceptions: request.availability.exceptions ? JSON.stringify(request.availability.exceptions) : null
      } : null,
      isActive: true
    };
  }

  private mapUpdateRequestToBackend(request: UpdateSpaceRequest): any {
    const { id, ...updateData } = request;
    return {
      name: updateData.name,
      description: updateData.description,
      type: updateData.type,
      capacity: updateData.capacity,
      location: updateData.location,
      floor: updateData.floor,
      area: updateData.area,
      amenities: updateData.amenities,
      rules: updateData.rules,
      hourlyRate: updateData.pricing?.hourlyRate,
      dailyRate: updateData.pricing?.dailyRate,
      weeklyRate: updateData.pricing?.weeklyRate,
      monthlyRate: updateData.pricing?.monthlyRate,
      equipment: updateData.equipment?.map(eq => ({
        name: eq.name,
        type: eq.type,
        brand: eq.brand,
        model: eq.model,
        quantity: eq.quantity,
        status: eq.status,
        description: eq.description
      })) || [],
      availability: updateData.availability ? {
        isActive: updateData.availability.isActive,
        advanceBookingDays: updateData.availability.advanceBookingDays,
        minBookingDuration: updateData.availability.minBookingDuration,
        maxBookingDuration: updateData.availability.maxBookingDuration,
        bufferTime: updateData.availability.bufferTime,
        weeklySchedule: JSON.stringify({
          monday: updateData.availability.schedule?.monday || { isOpen: false },
          tuesday: updateData.availability.schedule?.tuesday || { isOpen: false },
          wednesday: updateData.availability.schedule?.wednesday || { isOpen: false },
          thursday: updateData.availability.schedule?.thursday || { isOpen: false },
          friday: updateData.availability.schedule?.friday || { isOpen: false },
          saturday: updateData.availability.schedule?.saturday || { isOpen: false },
          sunday: updateData.availability.schedule?.sunday || { isOpen: false }
        }),
        exceptions: updateData.availability.exceptions ? JSON.stringify(updateData.availability.exceptions) : null
      } : null
    };
  }

  private getDefaultAvailability(): any {
    return {
      isActive: true,
      schedule: {
        monday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
        tuesday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
        wednesday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
        thursday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
        friday: { isOpen: true, openTime: '08:00', closeTime: '18:00', breaks: [] },
        saturday: { isOpen: false, openTime: '09:00', closeTime: '17:00', breaks: [] },
        sunday: { isOpen: false, openTime: '09:00', closeTime: '17:00', breaks: [] }
      },
      exceptions: [],
      advanceBookingDays: 30,
      minBookingDuration: 60,
      maxBookingDuration: 480,
      bufferTime: 15
    };
  }

  private getDefaultPricing(): any {
    return {
      hourlyRate: 0,
      dailyRate: 0,
      weeklyRate: 0,
      monthlyRate: 0,
      currency: 'MAD',
      discounts: []
    };
  }

  private getStatusColor(status: ReservationStatus): string {
    switch (status) {
      case ReservationStatus.CONFIRMED:
        return '#52c41a';
      case ReservationStatus.PENDING:
        return '#faad14';
      case ReservationStatus.CANCELLED:
        return '#ff4d4f';
      case ReservationStatus.COMPLETED:
        return '#1890ff';
      case ReservationStatus.NO_SHOW:
        return '#f5222d';
      default:
        return '#d9d9d9';
    }
  }
}
