<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="create-tenants-table" author="abdessamad">
        <createTable tableName="tenants">
            <column name="tenant_id" type="VARCHAR(30)">
                <constraints primaryKey="true" nullable="false" />
            </column>
            <column name="db_name" type="VARCHAR(50)">
                <constraints nullable="false" />
            </column>
            <column name="url" type="VARCHAR(100)">
                <constraints nullable="false" />
            </column>
            <column name="username" type="VARCHAR(50)">
                <constraints nullable="false" />
            </column>
            <column name="password" type="VARCHAR(100)">
                <constraints nullable="false" />
            </column>
            <column name="driver_class" type="VARCHAR(100)">
                <constraints nullable="false" />
            </column>
        </createTable>
    </changeSet>

    <changeSet id="add-version-column" author="abdessamad">
        <addColumn tableName="tenants">
            <column name="version" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>