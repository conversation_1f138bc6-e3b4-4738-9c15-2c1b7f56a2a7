<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- Create Site table -->
    <changeSet id="001-create-site-table" author="abounass">
        <createTable tableName="sites">
            <column name="site_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="city" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="country" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="address" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <!-- Audit fields from BaseEntity -->
            <column name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_selected_at" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Create indexes for better performance -->
        <createIndex tableName="sites" indexName="idx_site_name">
            <column name="name"/>
        </createIndex>

        <createIndex tableName="sites" indexName="idx_site_city">
            <column name="city"/>
        </createIndex>

        <createIndex tableName="sites" indexName="idx_site_country">
            <column name="country"/>
        </createIndex>

        <createIndex tableName="sites" indexName="idx_site_active">
            <column name="is_active"/>
        </createIndex>
    </changeSet>

    <!-- Insert sample data -->
    <changeSet id="002-insert-sample-sites" author="abounass">
        <insert tableName="sites">
            <column name="name" value="Workeem Paris"/>
            <column name="city" value="Paris"/>
            <column name="country" value="France"/>
            <column name="address" value="123 Avenue des Champs-Élysées, 75008 Paris"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="sites">
            <column name="name" value="Workeem Lyon"/>
            <column name="city" value="Lyon"/>
            <column name="country" value="France"/>
            <column name="address" value="45 Rue de la République, 69002 Lyon"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="sites">
            <column name="name" value="Workeem Marseille"/>
            <column name="city" value="Marseille"/>
            <column name="country" value="France"/>
            <column name="address" value="78 La Canebière, 13001 Marseille"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <insert tableName="sites">
            <column name="name" value="Workeem Bordeaux"/>
            <column name="city" value="Bordeaux"/>
            <column name="country" value="France"/>
            <column name="address" value="12 Cours de l'Intendance, 33000 Bordeaux"/>
            <column name="is_active" valueBoolean="false"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>
    </changeSet>

    <!-- Create spaces table -->
    <changeSet id="3" author="abounass">
        <createTable tableName="spaces">
            <column name="space_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="site_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="type" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="capacity" type="INT">
                <constraints nullable="true"/>
            </column>
            <column name="hourly_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="daily_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="monthly_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="floor" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="amenities" type="VARCHAR(1000)">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Create indexes for spaces -->
        <createIndex tableName="spaces" indexName="idx_space_site_id">
            <column name="site_id"/>
        </createIndex>

        <createIndex tableName="spaces" indexName="idx_space_name">
            <column name="name"/>
        </createIndex>

        <createIndex tableName="spaces" indexName="idx_space_type">
            <column name="type"/>
        </createIndex>

        <createIndex tableName="spaces" indexName="idx_space_active">
            <column name="is_active"/>
        </createIndex>
    </changeSet>

    <!-- Extend spaces table with additional fields -->
    <changeSet id="4" author="abounass">
        <addColumn tableName="spaces">
            <column name="location" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="area" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
            <column name="status" type="VARCHAR(50)" defaultValue="AVAILABLE">
                <constraints nullable="false"/>
            </column>
            <column name="images" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="rules" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="weekly_rate" type="DECIMAL(10,2)">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <!-- Create index for status -->
        <createIndex tableName="spaces" indexName="idx_space_status">
            <column name="status"/>
        </createIndex>
    </changeSet>

    <!-- Create equipment table -->
    <changeSet id="5" author="abounass">
        <createTable tableName="equipment">
            <column name="equipment_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="space_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="brand" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="model" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="quantity" type="INT" defaultValueNumeric="1">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(50)" defaultValue="WORKING">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Foreign key constraint -->
        <addForeignKeyConstraint
            baseTableName="equipment"
            baseColumnNames="space_id"
            constraintName="fk_equipment_space"
            referencedTableName="spaces"
            referencedColumnNames="space_id"
            onDelete="CASCADE"/>

        <!-- Create indexes -->
        <createIndex tableName="equipment" indexName="idx_equipment_space_id">
            <column name="space_id"/>
        </createIndex>
        <createIndex tableName="equipment" indexName="idx_equipment_type">
            <column name="type"/>
        </createIndex>
        <createIndex tableName="equipment" indexName="idx_equipment_status">
            <column name="status"/>
        </createIndex>
    </changeSet>

    <!-- Create space_availability table -->
    <changeSet id="6" author="abounass">
        <createTable tableName="space_availability">
            <column name="availability_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="space_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="is_active" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="advance_booking_days" type="INT" defaultValueNumeric="30">
                <constraints nullable="false"/>
            </column>
            <column name="min_booking_duration" type="INT" defaultValueNumeric="60">
                <constraints nullable="false"/>
            </column>
            <column name="max_booking_duration" type="INT" defaultValueNumeric="480">
                <constraints nullable="false"/>
            </column>
            <column name="buffer_time" type="INT" defaultValueNumeric="15">
                <constraints nullable="false"/>
            </column>
            <column name="weekly_schedule" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="exceptions" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Foreign key constraint -->
        <addForeignKeyConstraint
            baseTableName="space_availability"
            baseColumnNames="space_id"
            constraintName="fk_availability_space"
            referencedTableName="spaces"
            referencedColumnNames="space_id"
            onDelete="CASCADE"/>

        <!-- Create index -->
        <createIndex tableName="space_availability" indexName="idx_availability_space_id">
            <column name="space_id"/>
        </createIndex>
    </changeSet>

    <!-- Create space_pricing table -->
    <changeSet id="7" author="abounass">
        <createTable tableName="space_pricing">
            <column name="pricing_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="space_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="hourly_rate" type="DECIMAL(10,2)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="daily_rate" type="DECIMAL(10,2)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="weekly_rate" type="DECIMAL(10,2)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="monthly_rate" type="DECIMAL(10,2)" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="VARCHAR(10)" defaultValue="EUR">
                <constraints nullable="false"/>
            </column>
            <column name="discounts" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <!-- Foreign key constraint -->
        <addForeignKeyConstraint
            baseTableName="space_pricing"
            baseColumnNames="space_id"
            constraintName="fk_pricing_space"
            referencedTableName="spaces"
            referencedColumnNames="space_id"
            onDelete="CASCADE"/>

        <!-- Create index -->
        <createIndex tableName="space_pricing" indexName="idx_pricing_space_id">
            <column name="space_id"/>
        </createIndex>
    </changeSet>

    <!-- Insert sample spaces data -->
    <changeSet id="8" author="abounass">
        <!-- Poste de travail A1 -->
        <insert tableName="spaces">
            <column name="site_id" value="default-site"/>
            <column name="name" value="Poste de travail A1"/>
            <column name="description" value="Poste de travail individuel avec vue sur jardin"/>
            <column name="type" value="WORKSTATION"/>
            <column name="capacity" valueNumeric="1"/>
            <column name="location" value="Zone A"/>
            <column name="floor" value="RDC"/>
            <column name="area" valueNumeric="4.0"/>
            <column name="status" value="AVAILABLE"/>
            <column name="amenities" value="WiFi,Prise électrique,Éclairage LED"/>
            <column name="rules" value="Maintenir l'espace propre,Pas de nourriture,Silence requis"/>
            <column name="hourly_rate" valueNumeric="8.0"/>
            <column name="daily_rate" valueNumeric="50.0"/>
            <column name="weekly_rate" valueNumeric="300.0"/>
            <column name="monthly_rate" valueNumeric="1000.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Bureau privé B1 -->
        <insert tableName="spaces">
            <column name="site_id" value="default-site"/>
            <column name="name" value="Bureau privé B1"/>
            <column name="description" value="Bureau privé pour 2 personnes avec équipement complet"/>
            <column name="type" value="PRIVATE_OFFICE"/>
            <column name="capacity" valueNumeric="2"/>
            <column name="location" value="Zone B"/>
            <column name="floor" value="1er étage"/>
            <column name="area" valueNumeric="12.0"/>
            <column name="status" value="AVAILABLE"/>
            <column name="amenities" value="WiFi,Climatisation,Fenêtre,Tableau blanc"/>
            <column name="rules" value="Accès par badge,Nettoyage quotidien inclus,Pas de visiteurs sans autorisation"/>
            <column name="hourly_rate" valueNumeric="25.0"/>
            <column name="daily_rate" valueNumeric="180.0"/>
            <column name="weekly_rate" valueNumeric="1000.0"/>
            <column name="monthly_rate" valueNumeric="3500.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Salle de réunion Alpha -->
        <insert tableName="spaces">
            <column name="site_id" value="default-site"/>
            <column name="name" value="Salle de réunion Alpha"/>
            <column name="description" value="Salle de réunion moderne pour 8 personnes avec équipement audiovisuel"/>
            <column name="type" value="MEETING_ROOM"/>
            <column name="capacity" valueNumeric="8"/>
            <column name="location" value="Zone C"/>
            <column name="floor" value="2ème étage"/>
            <column name="area" valueNumeric="25.0"/>
            <column name="status" value="AVAILABLE"/>
            <column name="amenities" value="WiFi haut débit,Climatisation,Éclairage modulable,Isolation phonique"/>
            <column name="rules" value="Réservation obligatoire,Nettoyage après usage,Matériel à remettre en place"/>
            <column name="hourly_rate" valueNumeric="45.0"/>
            <column name="daily_rate" valueNumeric="320.0"/>
            <column name="weekly_rate" valueNumeric="1800.0"/>
            <column name="monthly_rate" valueNumeric="6000.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Espace Collaboratif Principal -->
        <insert tableName="spaces">
            <column name="site_id" value="default-site"/>
            <column name="name" value="Espace Collaboratif Principal"/>
            <column name="description" value="Grand espace ouvert pour le travail collaboratif, accessible sans réservation"/>
            <column name="type" value="COLLABORATIVE"/>
            <column name="capacity" valueNumeric="40"/>
            <column name="location" value="Rez-de-chaussée"/>
            <column name="floor" value="RDC"/>
            <column name="area" valueNumeric="120.0"/>
            <column name="status" value="AVAILABLE"/>
            <column name="amenities" value="WiFi haut débit,Prises électriques multiples,Éclairage naturel,Mobilier ergonomique,Espaces détente,Machine à café,Imprimante partagée"/>
            <column name="rules" value="Accès libre pendant les heures d'ouverture,Respect du silence relatif,Nettoyage après usage"/>
            <column name="hourly_rate" valueNumeric="0.0"/>
            <column name="daily_rate" valueNumeric="0.0"/>
            <column name="weekly_rate" valueNumeric="0.0"/>
            <column name="monthly_rate" valueNumeric="0.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>

        <!-- Cabine téléphonique 1 -->
        <insert tableName="spaces">
            <column name="site_id" value="default-site"/>
            <column name="name" value="Cabine téléphonique 1"/>
            <column name="description" value="Cabine isolée pour appels confidentiels"/>
            <column name="type" value="PHONE_BOOTH"/>
            <column name="capacity" valueNumeric="1"/>
            <column name="location" value="Zone A"/>
            <column name="floor" value="RDC"/>
            <column name="area" valueNumeric="2.0"/>
            <column name="status" value="AVAILABLE"/>
            <column name="amenities" value="Isolation phonique,Éclairage,Ventilation"/>
            <column name="rules" value="Durée maximale 30 minutes,Pas de nourriture"/>
            <column name="hourly_rate" valueNumeric="5.0"/>
            <column name="daily_rate" valueNumeric="30.0"/>
            <column name="weekly_rate" valueNumeric="150.0"/>
            <column name="monthly_rate" valueNumeric="500.0"/>
            <column name="is_active" valueBoolean="true"/>
            <column name="created_date" valueDate="now()"/>
            <column name="created_by" value="system"/>
        </insert>
    </changeSet>

</databaseChangeLog>