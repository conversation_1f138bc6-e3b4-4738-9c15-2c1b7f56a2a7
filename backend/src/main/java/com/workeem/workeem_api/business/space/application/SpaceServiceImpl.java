package com.workeem.workeem_api.business.space.application;

import com.workeem.workeem_api.business.space.domain.Equipment;
import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceAvailability;
import com.workeem.workeem_api.business.space.domain.SpaceStatus;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import com.workeem.workeem_api.business.space.infrastructure.EquipmentRepository;
import com.workeem.workeem_api.business.space.infrastructure.SpaceAvailabilityRepository;
import com.workeem.workeem_api.business.space.infrastructure.SpaceRepository;
import com.workeem.workeem_api.business.space.web.dto.EquipmentDto;
import com.workeem.workeem_api.business.space.web.dto.SpaceAvailabilityDto;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SpaceServiceImpl implements SpaceService {

    private final SpaceRepository spaceRepository;
    private final EquipmentRepository equipmentRepository;
    private final SpaceAvailabilityRepository spaceAvailabilityRepository;

    @Override
    @Transactional(readOnly = true)
    public List<Space> getAllSpaces(String siteId) {
        log.debug("Fetching all spaces for site: {}", siteId);
        return spaceRepository.findBySiteId(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Space> getActiveSpaces(String siteId) {
        log.debug("Fetching active spaces for site: {}", siteId);
        return spaceRepository.findBySiteIdAndIsActiveTrue(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public Space getSpaceById(Long id) {
        log.debug("Fetching space with ID: {}", id);
        return spaceRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Space with ID " + id + " not found"));
    }

    @Override
    @Transactional(readOnly = true)
    public List<Space> getSpacesByType(String siteId, SpaceType type) {
        log.debug("Fetching spaces by type {} for site: {}", type, siteId);
        return spaceRepository.findBySiteIdAndTypeAndIsActiveTrue(siteId, type);
    }

    @Override
    public Space createSpace(Space space) {
        log.debug("Creating new space: {}", space.getName());
        
        // Vérifier si le nom existe déjà pour ce site
        if (existsByName(space.getSiteId(), space.getName())) {
            throw new IllegalArgumentException("Space name already exists for this site");
        }
        
        // Définir les valeurs par défaut
        if (space.getStatus() == null) {
            space.setStatus(SpaceStatus.AVAILABLE);
        }
        if (space.getIsActive() == null) {
            space.setIsActive(true);
        }
        
        return spaceRepository.save(space);
    }

    @Override
    public Space createSpace(Space space, List<EquipmentDto> equipmentDtos) {
        return createSpace(space, equipmentDtos, null);
    }

    public Space createSpace(Space space, List<EquipmentDto> equipmentDtos, SpaceAvailabilityDto availabilityDto) {
        log.debug("Creating new space with equipment and availability: {}", space.getName());

        // Créer l'espace d'abord
        Space createdSpace = createSpace(space);

        // Créer les équipements si fournis
        if (equipmentDtos != null && !equipmentDtos.isEmpty()) {
            for (EquipmentDto equipmentDto : equipmentDtos) {
                Equipment equipment = new Equipment();
                equipment.setSpaceId(createdSpace.getSpaceId());
                equipment.setName(equipmentDto.getName());
                equipment.setType(equipmentDto.getType());
                equipment.setBrand(equipmentDto.getBrand());
                equipment.setModel(equipmentDto.getModel());
                equipment.setQuantity(equipmentDto.getQuantity() != null ? equipmentDto.getQuantity() : 1);
                equipment.setStatus(equipmentDto.getStatus() != null ? equipmentDto.getStatus() : com.workeem.workeem_api.business.space.domain.EquipmentStatus.WORKING);
                equipment.setDescription(equipmentDto.getDescription());

                equipmentRepository.save(equipment);
            }
        }

        // Créer les disponibilités si fournies
        if (availabilityDto != null) {
            createSpaceAvailability(createdSpace.getSpaceId(), availabilityDto);
        }

        return createdSpace;
    }

    @Override
    public Space updateSpace(Long spaceId, Space space) {
        log.debug("Updating space with ID: {}", spaceId);
        
        Space existingSpace = getSpaceById(spaceId);
        
        // Vérifier si le nouveau nom existe déjà (sauf pour l'espace actuel)
        if (!existingSpace.getName().equals(space.getName()) && 
            existsByName(space.getSiteId(), space.getName())) {
            throw new IllegalArgumentException("Space name already exists for this site");
        }
        
        // Mettre à jour les champs
        existingSpace.setName(space.getName());
        existingSpace.setDescription(space.getDescription());
        existingSpace.setType(space.getType());
        existingSpace.setCapacity(space.getCapacity());
        existingSpace.setLocation(space.getLocation());
        existingSpace.setFloor(space.getFloor());
        existingSpace.setArea(space.getArea());
        existingSpace.setAmenities(space.getAmenities());
        existingSpace.setRules(space.getRules());
        existingSpace.setImages(space.getImages());
        
        // Mettre à jour les tarifs
        existingSpace.setHourlyRate(space.getHourlyRate());
        existingSpace.setDailyRate(space.getDailyRate());
        existingSpace.setWeeklyRate(space.getWeeklyRate());
        existingSpace.setMonthlyRate(space.getMonthlyRate());
        
        return spaceRepository.save(existingSpace);
    }

    @Override
    public Space updateSpace(Long spaceId, Space space, List<EquipmentDto> equipmentDtos) {
        log.debug("Updating space with equipment: {}", spaceId);

        // Mettre à jour l'espace d'abord
        Space updatedSpace = updateSpace(spaceId, space);

        // Supprimer les anciens équipements
        equipmentRepository.deleteBySpaceId(spaceId);

        // Créer les nouveaux équipements si fournis
        if (equipmentDtos != null && !equipmentDtos.isEmpty()) {
            for (EquipmentDto equipmentDto : equipmentDtos) {
                Equipment equipment = new Equipment();
                equipment.setSpaceId(spaceId);
                equipment.setName(equipmentDto.getName());
                equipment.setType(equipmentDto.getType());
                equipment.setBrand(equipmentDto.getBrand());
                equipment.setModel(equipmentDto.getModel());
                equipment.setQuantity(equipmentDto.getQuantity() != null ? equipmentDto.getQuantity() : 1);
                equipment.setStatus(equipmentDto.getStatus() != null ? equipmentDto.getStatus() : com.workeem.workeem_api.business.space.domain.EquipmentStatus.WORKING);
                equipment.setDescription(equipmentDto.getDescription());

                equipmentRepository.save(equipment);
            }
        }

        return updatedSpace;
    }

    @Override
    public void deleteSpace(Long id) {
        log.debug("Deleting space with ID: {}", id);
        
        Space space = getSpaceById(id);
        spaceRepository.delete(space);
    }

    @Override
    public Space toggleSpaceStatus(Long spaceId) {
        log.debug("Toggling status for space with ID: {}", spaceId);
        
        Space space = getSpaceById(spaceId);
        space.setIsActive(!space.getIsActive());
        
        return spaceRepository.save(space);
    }

    @Override
    public Space updateSpaceStatus(Long spaceId, String status) {
        log.debug("Updating status to {} for space with ID: {}", status, spaceId);
        
        Space space = getSpaceById(spaceId);
        space.setStatus(SpaceStatus.valueOf(status.toUpperCase()));
        
        return spaceRepository.save(space);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String siteId, String name) {
        return spaceRepository.existsBySiteIdAndNameIgnoreCase(siteId, name);
    }

    @Override
    @Transactional(readOnly = true)
    public long countSpacesBySite(String siteId) {
        return spaceRepository.countBySiteId(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EquipmentDto> getEquipmentBySpaceId(Long spaceId) {
        log.debug("Fetching equipment for space: {}", spaceId);
        List<Equipment> equipment = equipmentRepository.findBySpaceId(spaceId);

        return equipment.stream()
                .map(eq -> EquipmentDto.builder()
                        .id(eq.getEquipmentId())
                        .name(eq.getName())
                        .type(eq.getType())
                        .brand(eq.getBrand())
                        .model(eq.getModel())
                        .quantity(eq.getQuantity())
                        .status(eq.getStatus())
                        .description(eq.getDescription())
                        .build())
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * Create space availability
     */
    private void createSpaceAvailability(Long spaceId, SpaceAvailabilityDto availabilityDto) {
        log.debug("Creating availability for space: {}", spaceId);

        SpaceAvailability availability = new SpaceAvailability();
        availability.setSpaceId(spaceId);
        availability.setIsActive(availabilityDto.getIsActive() != null ? availabilityDto.getIsActive() : true);
        availability.setAdvanceBookingDays(availabilityDto.getAdvanceBookingDays() != null ? availabilityDto.getAdvanceBookingDays() : 30);
        availability.setMinBookingDuration(availabilityDto.getMinBookingDuration() != null ? availabilityDto.getMinBookingDuration() : 60);
        availability.setMaxBookingDuration(availabilityDto.getMaxBookingDuration() != null ? availabilityDto.getMaxBookingDuration() : 480);
        availability.setBufferTime(availabilityDto.getBufferTime() != null ? availabilityDto.getBufferTime() : 15);

        // Utiliser le planning hebdomadaire fourni ou créer un planning par défaut
        if (availabilityDto.getWeeklySchedule() != null && !availabilityDto.getWeeklySchedule().isEmpty()) {
            availability.setWeeklySchedule(availabilityDto.getWeeklySchedule());
        } else {
            // Planning par défaut : ouvert du lundi au vendredi de 9h à 18h
            String defaultSchedule = "{\"monday\":{\"isOpen\":true,\"openTime\":\"09:00\",\"closeTime\":\"18:00\"}," +
                                    "\"tuesday\":{\"isOpen\":true,\"openTime\":\"09:00\",\"closeTime\":\"18:00\"}," +
                                    "\"wednesday\":{\"isOpen\":true,\"openTime\":\"09:00\",\"closeTime\":\"18:00\"}," +
                                    "\"thursday\":{\"isOpen\":true,\"openTime\":\"09:00\",\"closeTime\":\"18:00\"}," +
                                    "\"friday\":{\"isOpen\":true,\"openTime\":\"09:00\",\"closeTime\":\"18:00\"}," +
                                    "\"saturday\":{\"isOpen\":false}," +
                                    "\"sunday\":{\"isOpen\":false}}";
            availability.setWeeklySchedule(defaultSchedule);
        }

        // Gérer les exceptions si fournies
        if (availabilityDto.getExceptions() != null && !availabilityDto.getExceptions().isEmpty()) {
            availability.setExceptions(availabilityDto.getExceptions());
        }

        spaceAvailabilityRepository.save(availability);
    }
}
